/**
 * Audio alert system for timer notifications
 */

/**
 * Audio Context manager for sound generation and playback
 */
class AlertSystem {
  constructor() {
    this.audioContext = null;
    this.isUnlocked = false;
    this.alertSounds = new Map();
    this.masterVolume = 0.3;
    
    this.initializeAudioContext();
    this.setupUnlockListeners();
    this.generateAlertSounds();
  }

  /**
   * Initialize the Web Audio API context
   */
  initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // Check if context is already running
      if (this.audioContext.state === 'running') {
        this.handleAudioUnlock();
      }
    } catch (error) {
      console.warn('Web Audio API not supported:', error);
    }
  }

  /**
   * Setup listeners for user interaction to unlock audio
   */
  setupUnlockListeners() {
    const unlockAudio = () => {
      if (this.audioContext && this.audioContext.state === 'suspended') {
        this.audioContext.resume().then(() => {
          this.handleAudioUnlock();
        }).catch(error => {
          console.warn('Failed to resume audio context:', error);
        });
      }
    };

    // Listen for any user interaction
    const events = ['click', 'keydown', 'touchstart'];
    events.forEach(eventType => {
      document.addEventListener(eventType, unlockAudio, { once: true });
    });

    // Specific unlock button
    const unlockBtn = Utils.DOM.getId('audioUnlockBtn');
    if (unlockBtn) {
      Utils.Events.on(unlockBtn, 'click', () => {
        unlockAudio();
      });
    }
  }

  /**
   * Handle audio context unlock
   */
  handleAudioUnlock() {
    this.isUnlocked = true;
    
    // Hide the unlock button
    const unlockBtn = Utils.DOM.getId('audioUnlockBtn');
    if (unlockBtn) {
      unlockBtn.style.display = 'none';
    }

    // Dispatch unlock event
    Utils.Events.dispatch(document, 'audioUnlocked');
    
    console.log('Audio context unlocked');
  }

  /**
   * Generate predefined alert sounds using frequency synthesis
   */
  generateAlertSounds() {
    this.alertSounds.set('Default', {
      name: 'Default Bell',
      frequencies: [800, 1000, 800],
      durations: [0.1, 0.1, 0.1],
      volumes: [0.3, 0.4, 0.3]
    });

    this.alertSounds.set('Gentle', {
      name: 'Gentle Chime',
      frequencies: [523, 659, 784],
      durations: [0.3, 0.3, 0.4],
      volumes: [0.2, 0.25, 0.2]
    });

    this.alertSounds.set('Bell', {
      name: 'Church Bell',
      frequencies: [440, 880, 440],
      durations: [0.5, 0.3, 0.5],
      volumes: [0.4, 0.3, 0.4]
    });

    this.alertSounds.set('Notification', {
      name: 'Notification',
      frequencies: [1000, 1200],
      durations: [0.1, 0.2],
      volumes: [0.3, 0.3]
    });

    this.alertSounds.set('Urgent', {
      name: 'Urgent Alert',
      frequencies: [1400, 1000, 1400, 1000],
      durations: [0.1, 0.1, 0.1, 0.1],
      volumes: [0.4, 0.3, 0.4, 0.3]
    });

    this.alertSounds.set('Peaceful', {
      name: 'Peaceful Tone',
      frequencies: [261, 329, 392],
      durations: [0.4, 0.4, 0.6],
      volumes: [0.2, 0.2, 0.2]
    });

    this.alertSounds.set('Digital', {
      name: 'Digital Beep',
      frequencies: [1200],
      durations: [0.15],
      volumes: [0.35]
    });

    this.alertSounds.set('Classic', {
      name: 'Classic Alarm',
      frequencies: [800, 1000, 800, 1000],
      durations: [0.2, 0.2, 0.2, 0.2],
      volumes: [0.3, 0.3, 0.3, 0.3]
    });

    this.alertSounds.set('None', {
      name: 'None (Silent)',
      frequencies: [],
      durations: [],
      volumes: []
    });
  }

  /**
   * Get list of available alert sounds
   * @returns {Array} Array of alert sound options
   */
  getAlertSounds() {
    return Array.from(this.alertSounds.entries()).map(([key, sound]) => ({
      value: key,
      name: sound.name
    }));
  }

  /**
   * Play an alert sound
   * @param {string} alertType - Type of alert to play
   * @returns {Promise} Promise that resolves when sound finishes
   */
  async playAlert(alertType = 'Default') {
    // Handle silent/none alert
    if (alertType === 'None') {
      return Promise.resolve();
    }

    if (!this.audioContext || !this.isUnlocked) {
      console.warn('Audio context not available or not unlocked');
      return Promise.resolve();
    }

    const sound = this.alertSounds.get(alertType);
    if (!sound) {
      console.warn(`Alert sound '${alertType}' not found`);
      return Promise.resolve();
    }

    try {
      await this.playToneSequence(sound);
    } catch (error) {
      console.error('Error playing alert:', error);
    }
  }

  /**
   * Play a sequence of tones
   * @param {Object} sound - Sound configuration
   * @returns {Promise} Promise that resolves when sequence finishes
   */
  playToneSequence(sound) {
    return new Promise((resolve) => {
      const { frequencies, durations, volumes } = sound;
      let currentTime = this.audioContext.currentTime;

      frequencies.forEach((frequency, index) => {
        const duration = durations[index] || durations[0];
        const volume = (volumes[index] || volumes[0]) * this.masterVolume;

        this.playTone(frequency, duration, volume, currentTime);
        currentTime += duration + 0.05; // Small gap between tones
      });

      // Resolve after all tones complete
      setTimeout(() => resolve(), currentTime * 1000 - this.audioContext.currentTime * 1000 + 100);
    });
  }

  /**
   * Play a single tone
   * @param {number} frequency - Frequency in Hz
   * @param {number} duration - Duration in seconds
   * @param {number} volume - Volume (0-1)
   * @param {number} startTime - Start time in audio context time
   */
  playTone(frequency, duration, volume, startTime) {
    if (!this.audioContext) return;

    // Create oscillator for the tone
    const oscillator = this.audioContext.createOscillator();
    const gainNode = this.audioContext.createGain();
    
    // Connect nodes
    oscillator.connect(gainNode);
    gainNode.connect(this.audioContext.destination);
    
    // Configure oscillator
    oscillator.frequency.setValueAtTime(frequency, startTime);
    oscillator.type = 'sine';
    
    // Configure envelope (attack, sustain, release)
    const attackTime = 0.01;
    const releaseTime = 0.1;
    const sustainTime = duration - attackTime - releaseTime;
    
    gainNode.gain.setValueAtTime(0, startTime);
    gainNode.gain.linearRampToValueAtTime(volume, startTime + attackTime);
    gainNode.gain.setValueAtTime(volume, startTime + attackTime + sustainTime);
    gainNode.gain.linearRampToValueAtTime(0, startTime + duration);
    
    // Start and stop oscillator
    oscillator.start(startTime);
    oscillator.stop(startTime + duration);
  }

  /**
   * Test an alert sound
   * @param {string} alertType - Type of alert to test
   */
  testAlert(alertType) {
    if (alertType === 'None') {
      // Show a brief visual feedback for silent alert testing
      console.log('Testing silent alert - no sound will play');
      return;
    }
    this.playAlert(alertType);
  }

  /**
   * Set master volume
   * @param {number} volume - Volume (0-1)
   */
  setVolume(volume) {
    this.masterVolume = Math.max(0, Math.min(1, volume));
  }

  /**
   * Get current volume
   * @returns {number} Current volume (0-1)
   */
  getVolume() {
    return this.masterVolume;
  }

  /**
   * Check if audio is available and unlocked
   * @returns {boolean} True if audio is ready
   */
  isAudioReady() {
    return this.audioContext && this.isUnlocked;
  }

  /**
   * Dispose of audio resources
   */
  dispose() {
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    this.isUnlocked = false;
    this.alertSounds.clear();
  }
}

// Create global alert system instance
const alertSystem = new AlertSystem();

// Export to global scope
window.AlertSystem = AlertSystem;
window.alerts = alertSystem;