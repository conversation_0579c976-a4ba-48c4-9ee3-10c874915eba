/**
 * Session management for the collaborative timer server
 */

/**
 * Session manager class for handling real-time collaboration
 */
class SessionManager {
  constructor() {
    this.sessions = new Map();
    this.messageHandlers = new Map();
    this.cleanupInterval = null;
    
    this.setupMessageHandlers();
    this.startCleanupTimer();
  }

  /**
   * Setup message handlers for different message types
   */
  setupMessageHandlers() {
    this.messageHandlers.set('join_session', this.handleJoinSession.bind(this));
    this.messageHandlers.set('segments_update', this.handleSegmentsUpdate.bind(this));
    this.messageHandlers.set('timer_start', this.handleTimerStart.bind(this));
    this.messageHandlers.set('timer_pause', this.handleTimerPause.bind(this));
    this.messageHandlers.set('timer_stop', this.handleTimerStop.bind(this));
    this.messageHandlers.set('timer_repeat', this.handleTimerRepeat.bind(this));
    this.messageHandlers.set('timer_next_segment', this.handleTimerNextSegment.bind(this));
    this.messageHandlers.set('user_update', this.handleUserUpdate.bind(this));
  }

  /**
   * Handle incoming WebSocket message
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Parsed message object
   */
  handleMessage(ws, message) {
    const handler = this.messageHandlers.get(message.type);
    
    if (handler) {
      try {
        handler(ws, message);
      } catch (error) {
        console.error(`Error handling message ${message.type}:`, error);
        this.sendError(ws, `Failed to process ${message.type}`);
      }
    } else {
      console.warn(`Unknown message type: ${message.type}`);
      this.sendError(ws, 'Unknown message type');
    }
  }

  /**
   * Handle join session request
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Join session message
   */
  handleJoinSession(ws, message) {
    const { sessionId, clientId } = message;
    
    // Validate session ID
    if (!sessionId || !this.isValidSessionId(sessionId)) {
      return this.sendError(ws, 'Invalid session ID');
    }

    // Validate or generate client ID
    let validClientId = clientId;
    if (!validClientId || !this.isValidClientId(validClientId)) {
      validClientId = this.generateClientId();
    }

    // Store client info on WebSocket
    ws.sessionId = sessionId;
    ws.clientId = validClientId;

    // Get or create session
    let session = this.sessions.get(sessionId);
    const isNewSession = !session;

    if (isNewSession) {
      session = this.createSession(sessionId);
      this.sessions.set(sessionId, session);
    }

    // Add user to session
    const user = {
      clientId: validClientId,
      name: '',
      email: '',
      lastSeen: Date.now(),
      isOnline: true,
      ws
    };

    session.users.set(validClientId, user);

    if (isNewSession) {
      // Send session created response
      this.sendMessage(ws, {
        type: 'session_created',
        sessionId,
        clientId: validClientId,
        session: this.getPublicSessionData(session)
      });
    } else {
      // Send session joined response
      this.sendMessage(ws, {
        type: 'session_joined',
        sessionId,
        clientId: validClientId,
        session: this.getPublicSessionData(session)
      });
    }

    // Notify other users of new connection
    this.broadcastToSession(sessionId, {
      type: 'user_connected',
      sessionId,
      user: this.getPublicUserData(user)
    }, validClientId);

    console.log(`Client ${validClientId} joined session ${sessionId}`);
  }

  /**
   * Handle segments update
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Segments update message
   */
  handleSegmentsUpdate(ws, message) {
    const { sessionId, segments } = message;
    const session = this.getSessionForClient(ws, sessionId);
    
    if (!session) return;

    // Validate segments structure
    if (!segments || !segments.items || !Array.isArray(segments.items)) {
      return this.sendError(ws, 'Invalid segments data');
    }

    // Update session segments
    session.segments = {
      lastUpdated: segments.lastUpdated || Date.now(),
      items: segments.items.map(item => ({
        name: String(item.name || 'Segment'),
        duration: Math.max(1, parseInt(item.duration) || 1500),
        alert: String(item.alert || 'Default'),
        customCSS: String(item.customCSS || '')
      }))
    };

    // Handle timer state when segments change
    if (session.timer.currentSegment >= session.segments.items.length) {
      // Current segment was deleted, reset to first segment
      session.timer.currentSegment = 0;
      session.timer.timeRemaining = session.segments.items[0]?.duration || 1500;
      if (!session.timer.isRunning) {
        session.timer.startedAt = 0;
        session.timer.startedSegment = 0;
        session.timer.pausedAt = 0;
        session.timer.timePaused = 0;
      }
    } else if (session.timer.isRunning) {
      // Timer is running and segment durations may have changed
      // Recalculate baseline to maintain accuracy
      const now = Date.now();
      const currentPosition = now - session.timer.startedAt - session.timer.timePaused - (session.timer.isPaused && session.timer.pausedAt > 0 ? now - session.timer.pausedAt : 0);

      // Update baseline
      session.timer.startedAt = now - currentPosition;
      session.timer.startedSegment = session.timer.currentSegment;
      session.timer.timePaused = 0;
      session.timer.pausedAt = 0;
    } else {
      // Timer is stopped, just update remaining time if needed
      const currentSegmentDuration = session.segments.items[session.timer.currentSegment]?.duration || 1500;
      if (session.timer.timeRemaining > currentSegmentDuration) {
        session.timer.timeRemaining = currentSegmentDuration;
      }
    }

    // Broadcast segments update to all clients except the sender
    this.broadcastToSession(sessionId, {
      type: 'segments_updated',
      sessionId,
      segments: session.segments
    }, ws.clientId);

    console.log(`Segments updated for session ${sessionId}`);
  }

  /**
   * Handle timer start
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Timer start message
   */
  handleTimerStart(ws, message) {
    const { sessionId } = message;
    const session = this.getSessionForClient(ws, sessionId);

    if (!session) return;

    const now = Date.now();

    // Handle resume from pause
    if (session.timer.isPaused && session.timer.pausedAt > 0) {
      this.resume(session);
    } else if (!session.timer.isRunning) {
      // Starting fresh
      session.timer.startedAt = now;
      session.timer.startedSegment = session.timer.currentSegment;
      session.timer.timePaused = 0;
    }

    // Update timer state
    session.timer.isRunning = true;
    session.timer.isPaused = false;
    session.timer.pausedAt = 0;

    // Broadcast timer update
    this.broadcast(sessionId, session);

    console.log(`Timer started for session ${sessionId}`);
  }

  /**
   * Handle timer pause
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Timer pause message
   */
  handleTimerPause(ws, message) {
    const { sessionId } = message;
    const session = this.getSessionForClient(ws, sessionId);
    
    if (!session) return;

    const now = Date.now();
    
    // Update timer state
    session.timer.isPaused = true;
    session.timer.pausedAt = now;

    // Broadcast timer update
    this.broadcast(sessionId, session);

    console.log(`Timer paused for session ${sessionId}`);
  }

  /**
   * Handle timer stop
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Timer stop message
   */
  handleTimerStop(ws, message) {
    const { sessionId } = message;
    const session = this.getSessionForClient(ws, sessionId);
    
    if (!session) return;

    // Reset timer state
    session.timer.isRunning = false;
    session.timer.isPaused = false;
    session.timer.currentSegment = 0;
    session.timer.timeRemaining = session.segments.items[0]?.duration || 1500;
    session.timer.startedAt = 0;
    session.timer.startedSegment = 0;
    session.timer.pausedAt = 0;
    session.timer.timePaused = 0;

    // Broadcast timer update
    this.broadcast(sessionId, session);

    console.log(`Timer stopped for session ${sessionId}`);
  }

  /**
   * Handle timer repeat toggle
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Timer repeat message
   */
  handleTimerRepeat(ws, message) {
    const { sessionId } = message;
    const session = this.getSessionForClient(ws, sessionId);
    
    if (!session) return;

    // Toggle repeat mode
    session.timer.repeat = !session.timer.repeat;

    // Broadcast timer update
    this.broadcast(sessionId, session);

    console.log(`Timer repeat ${session.timer.repeat ? 'enabled' : 'disabled'} for session ${sessionId}`);
  }

  /**
   * Handle timer next segment
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Timer next segment message
   */
  handleTimerNextSegment(ws, message) {
    const { sessionId } = message;
    const session = this.getSessionForClient(ws, sessionId);
    
    if (!session) return;

    const nextSegmentIndex = session.timer.currentSegment + 1;
    const totalSegments = session.segments.items.length;

    if (nextSegmentIndex >= totalSegments) {
      // Reset to first segment if at the end
      session.timer.currentSegment = 0;
    } else {
      // Move to next segment
      session.timer.currentSegment = nextSegmentIndex;
    }

    // Reset timer for new segment
    const newSegmentDuration = session.segments.items[session.timer.currentSegment]?.duration || 1500;
    session.timer.timeRemaining = newSegmentDuration;

    // If timer was running, reset the start time and segment
    if (session.timer.isRunning && !session.timer.isPaused) {
      session.timer.startedAt = Date.now();
      session.timer.startedSegment = session.timer.currentSegment;
      session.timer.timePaused = 0;
    }

    // Broadcast timer update
    this.broadcast(sessionId, session);

    console.log(`Next segment for session ${sessionId} - now on segment ${session.timer.currentSegment + 1}`);
  }

  /**
   * Handle user update
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - User update message
   */
  handleUserUpdate(ws, message) {
    const { sessionId, user } = message;
    const session = this.getSessionForClient(ws, sessionId);
    
    if (!session) return;

    const existingUser = session.users.get(ws.clientId);
    if (!existingUser) return;

    // Update user data
    if (user.name !== undefined) {
      existingUser.name = String(user.name).trim();
    }
    if (user.email !== undefined) {
      existingUser.email = String(user.email).trim();
    }
    existingUser.lastSeen = Date.now();

    // Broadcast user update to all clients
    this.broadcastToSession(sessionId, {
      type: 'user_updated',
      sessionId,
      user: this.getPublicUserData(existingUser)
    });

    console.log(`User ${ws.clientId} updated profile in session ${sessionId}`);
  }

  /**
   * Handle timer resume after pause
   * @param {Object} session - Session object
   */
  resume(session) {
    const now = Date.now();

    if (session.timer.pausedAt > 0) {
      // Add pause duration to total paused time
      session.timer.timePaused += now - session.timer.pausedAt;
      session.timer.pausedAt = 0;
    }
  }

  /**
   * Calculate current timer state based on elapsed time
   * @param {Object} session - Session object
   */
  update(session) {
    if (!session.timer.isRunning || !session.timer.startedAt) {
      return;
    }

    const now = Date.now();
    const segments = session.segments.items;

    if (segments.length === 0) return;

    // Calculate actual elapsed time
    const actualElapsed = now - session.timer.startedAt - session.timer.timePaused - (session.timer.isPaused && session.timer.pausedAt > 0 ? now - session.timer.pausedAt : 0);

    // Find current segment starting from startedSegment
    let currentSegment = session.timer.startedSegment;
    let remainingElapsed = actualElapsed;

    while (remainingElapsed > 0) {
      const segmentDuration = segments[currentSegment].duration * 1000;
      if (remainingElapsed < segmentDuration) break;

      remainingElapsed -= segmentDuration;
      currentSegment++;

      // Handle repeat mode wraparound
      if (currentSegment >= segments.length && session.timer.repeat) {
        currentSegment = 0;
      }

      // Check if timer should stop (no repeat and reached end)
      if (currentSegment >= segments.length && !session.timer.repeat) {
        session.timer.isRunning = false;
        session.timer.isPaused = false;
        session.timer.currentSegment = 0;
        session.timer.timeRemaining = segments[0].duration;
        session.timer.startedAt = 0;
        session.timer.startedSegment = 0;
        session.timer.pausedAt = 0;
        session.timer.timePaused = 0;
        return;
      }
    }

    // Calculate remaining time in current segment
    const timeRemaining = Math.max(0, Math.floor((segments[currentSegment].duration * 1000 - remainingElapsed) / 1000));

    // Update session state
    session.timer.currentSegment = currentSegment;
    session.timer.timeRemaining = timeRemaining;
  }

  /**
   * Broadcast timer update to all clients in session
   * @param {string} sessionId - Session ID
   * @param {Object} session - Session object
   */
  broadcast(sessionId, session) {
    // Calculate current timer state before broadcasting
    this.update(session);
    
    this.broadcastToSession(sessionId, {
      type: 'timer_updated',
      sessionId,
      currentSegment: session.timer.currentSegment,
      timeRemaining: session.timer.timeRemaining,
      isRunning: session.timer.isRunning,
      isPaused: session.timer.isPaused,
      repeat: session.timer.repeat
    });
  }

  /**
   * Remove client from session
   * @param {string} sessionId - Session ID
   * @param {string} clientId - Client ID
   */
  removeClient(sessionId, clientId) {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const user = session.users.get(clientId);
    if (user) {
      // Mark user as offline
      user.isOnline = false;
      user.lastSeen = Date.now();

      // Remove user from session
      session.users.delete(clientId);

      // Broadcast user disconnection
      this.broadcastToSession(sessionId, {
        type: 'user_disconnected',
        sessionId,
        user: this.getPublicUserData(user)
      });

      console.log(`Client ${clientId} left session ${sessionId}`);

      // Clean up empty sessions
      if (session.users.size === 0) {
        this.sessions.delete(sessionId);
        console.log(`Session ${sessionId} cleaned up (empty)`);
      }
    }
  }

  /**
   * Broadcast message to all clients in a session
   * @param {string} sessionId - Session ID
   * @param {Object} message - Message to broadcast
   * @param {string} excludeClientId - Client ID to exclude from broadcast
   */
  broadcastToSession(sessionId, message, excludeClientId = null) {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.users.forEach((user, clientId) => {
      if (clientId !== excludeClientId && user.ws && user.ws.readyState === 1) {
        this.sendMessage(user.ws, message);
      }
    });
  }

  /**
   * Send message to WebSocket client
   * @param {WebSocket} ws - WebSocket connection
   * @param {Object} message - Message to send
   */
  sendMessage(ws, message) {
    if (ws.readyState === 1) { // WebSocket.OPEN
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
      }
    }
  }

  /**
   * Send error message to WebSocket client
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} errorMessage - Error message
   */
  sendError(ws, errorMessage) {
    this.sendMessage(ws, {
      type: 'error',
      message: errorMessage
    });
  }

  /**
   * Create new session
   * @param {string} sessionId - Session ID
   * @returns {Object} New session object
   */
  createSession(sessionId) {
    return {
      id: sessionId,
      name: 'Pomodoro Session',
      description: 'Focus and break timer',
      createdAt: Date.now(),
      lastActivity: Date.now(),
      users: new Map(),
      segments: {
        lastUpdated: Date.now(),
        items: [
          {
            name: 'Focus',
            duration: 1500, // 25 minutes
            alert: 'Default',
            customCSS: ''
          },
          {
            name: 'Break',
            duration: 300, // 5 minutes
            alert: 'Default',
            customCSS: ''
          }
        ]
      },
      timer: {
        repeat: false,
        currentSegment: 0,
        timeRemaining: 1500,
        isRunning: false,
        isPaused: false,
        startedAt: 0,
        startedSegment: 0,
        pausedAt: 0,
        timePaused: 0
      }
    };
  }

  /**
   * Get session for WebSocket client with validation
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} sessionId - Session ID
   * @returns {Object|null} Session object or null
   */
  getSessionForClient(ws, sessionId) {
    if (!sessionId || sessionId !== ws.sessionId) {
      this.sendError(ws, 'Invalid session');
      return null;
    }

    const session = this.sessions.get(sessionId);
    if (!session) {
      this.sendError(ws, 'Session not found');
      return null;
    }

    // Update last activity
    session.lastActivity = Date.now();
    
    return session;
  }

  /**
   * Get public session data (without sensitive information)
   * @param {Object} session - Session object
   * @returns {Object} Public session data
   */
  getPublicSessionData(session) {
    // Calculate current timer state before sending
    this.update(session);
    
    return {
      name: session.name,
      description: session.description,
      segments: session.segments,
      timer: {
        repeat: session.timer.repeat,
        currentSegment: session.timer.currentSegment,
        timeRemaining: session.timer.timeRemaining,
        isRunning: session.timer.isRunning,
        isPaused: session.timer.isPaused
      },
      users: Array.from(session.users.values()).map(user => this.getPublicUserData(user))
    };
  }

  /**
   * Get public user data (without sensitive information)
   * @param {Object} user - User object
   * @returns {Object} Public user data
   */
  getPublicUserData(user) {
    return {
      clientId: user.clientId,
      name: user.name,
      email: user.email,
      lastSeen: user.lastSeen,
      isOnline: user.isOnline
    };
  }

  /**
   * Validate session ID format
   * @param {string} sessionId - Session ID to validate
   * @returns {boolean} True if valid
   */
  isValidSessionId(sessionId) {
    return typeof sessionId === 'string' && /^[a-z0-9-]{3,20}$/.test(sessionId);
  }

  /**
   * Validate client ID format
   * @param {string} clientId - Client ID to validate
   * @returns {boolean} True if valid
   */
  isValidClientId(clientId) {
    return typeof clientId === 'string' && /^[a-f0-9-]{36}$/.test(clientId);
  }

  /**
   * Generate new client ID
   * @returns {string} New client ID
   */
  generateClientId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Start cleanup timer for inactive sessions
   */
  startCleanupTimer() {
    const cleanupInterval = parseInt(process.env.SESSION_CLEANUP_INTERVAL) || 300000; // 5 minutes
    
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveSessions();
    }, cleanupInterval);
  }

  /**
   * Clean up inactive sessions
   */
  cleanupInactiveSessions() {
    const now = Date.now();
    const maxInactiveTime = 30 * 60 * 1000; // 30 minutes
    let cleanedCount = 0;

    this.sessions.forEach((session, sessionId) => {
      if (now - session.lastActivity > maxInactiveTime || session.users.size === 0) {
        this.sessions.delete(sessionId);
        cleanedCount++;
      }
    });

    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} inactive sessions`);
    }
  }

  /**
   * Get session by ID
   * @param {string} sessionId - Session ID
   * @returns {Object|null} Session object or null
   */
  getSession(sessionId) {
    return this.sessions.get(sessionId) || null;
  }

  /**
   * Get total number of sessions
   * @returns {number} Session count
   */
  getSessionCount() {
    return this.sessions.size;
  }

  /**
   * Get server statistics
   * @returns {Object} Server statistics
   */
  getStats() {
    let totalUsers = 0;
    let activeTimers = 0;
    
    this.sessions.forEach(session => {
      totalUsers += session.users.size;
      if (session.timer.isRunning) {
        activeTimers++;
      }
    });

    return {
      sessions: this.sessions.size,
      totalUsers,
      activeTimers,
      uptime: process.uptime()
    };
  }

  /**
   * Dispose session manager
   */
  dispose() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    this.sessions.clear();
    this.messageHandlers.clear();
  }
}

module.exports = SessionManager;