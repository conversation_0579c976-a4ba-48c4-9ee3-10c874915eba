/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: #1a1a1a;
  color: #e0e0e0;
  line-height: 1.6;
}

/* App container */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
}

/* Audio unlock button */
.audio-unlock-btn {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: #333;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  color: #ffa500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.audio-unlock-btn:hover {
  background: #444;
  transform: scale(1.05);
}

.audio-unlock-btn:active {
  transform: scale(0.95);
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 80px;
  flex-wrap: wrap;
  gap: 15px;
}

.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 200px;
}

.header-right {
  justify-content: flex-end;
}

.header-center {
  text-align: center;
  flex: 2;
  min-width: 250px;
}

.session-name {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #ffffff;
}

.session-description {
  font-size: 0.9rem;
  color: #b0b0b0;
  margin: 0;
}

/* Connected users */
.connected-users {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-count {
  font-size: 0.9rem;
  color: #b0b0b0;
}

/* User button */
.user-btn {
  position: relative;
  background: none;
  border: 3px solid #4a5568;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  cursor: pointer;
  padding: 0;
  transition: all 0.3s ease;
}

.user-btn:hover {
  transform: scale(1.05);
  border-color: #68d391;
}

.user-btn.online {
  border-color: #48bb78;
}

.user-btn.offline {
  border-color: #f56565;
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #48bb78;
  border: 2px solid #1a1a1a;
}

.user-btn.offline .user-status-indicator {
  background: #f56565;
}

/* Buttons */
.btn {
  background: #4a5568;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  font-family: inherit;
}

.btn:hover {
  background: #68d391;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(104, 211, 145, 0.3);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: #68d391;
  color: #1a202c;
}

.btn-primary:hover {
  background: #9ae6b4;
}

.btn-secondary {
  background: #4a5568;
}

.btn-secondary:hover {
  background: #68d391;
}

.btn-small {
  padding: 6px 12px;
  font-size: 0.8rem;
}

/* Timer styles based on state */
.timer-running {
  background: linear-gradient(135deg, #1a4a1a 0%, #2a5a2a 100%);
}

.timer-paused {
  background: linear-gradient(135deg, #4a4a1a 0%, #5a5a2a 100%);
}

.timer-repeat .repeat-indicator {
  color: #ffa500;
}

/* Main content */
.main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.timer-container {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.segment-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 0 20px;
}

.segment-name {
  font-size: 1.4rem;
  font-weight: 600;
  color: #68d391;
}

.segment-progress {
  font-size: 1rem;
  color: #b0b0b0;
}

.timer-display {
  font-size: 6rem;
  font-weight: 300;
  color: #ffffff;
  margin-bottom: 40px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.timer-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.timer-btn {
  min-width: 100px;
  padding: 12px 24px;
  font-size: 1rem;
}

/* Connection status */
.connection-status {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  font-size: 0.8rem;
  color: #b0b0b0;
  backdrop-filter: blur(10px);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #f56565;
  transition: background-color 0.3s ease;
}

.connection-status.connected .status-indicator {
  background: #48bb78;
}

.connection-status.connecting .status-indicator {
  background: #ffa500;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.modal.show {
  display: flex;
}

.modal-content {
  background: #2a2a2a;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
  margin: 0;
  font-size: 1.4rem;
  color: #ffffff;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #b0b0b0;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.modal-body {
  padding: 25px;
}

/* Forms */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #e0e0e0;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  background: #3a3a3a;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  color: #ffffff;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #68d391;
  box-shadow: 0 0 0 3px rgba(104, 211, 145, 0.1);
}

.form-control::placeholder {
  color: #888;
}

.form-help {
  display: block;
  margin-top: 4px;
  font-size: 0.8rem;
  color: #888;
}

/* Checkbox */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  background: #3a3a3a;
  border: 1px solid #4a4a4a;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: #68d391;
  border-color: #68d391;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 8px;
  border: solid #1a202c;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Settings sections */
.settings-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.settings-section h3 {
  margin-bottom: 20px;
  color: #ffffff;
  font-size: 1.1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.segment-actions {
  display: flex;
  gap: 8px;
}

/* Segments list */
.segments-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.segment-item {
  background: #333;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.segment-item.expanded {
  background: #363636;
}

.segment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.segment-header:hover {
  background: rgba(255, 255, 255, 0.05);
}

.segment-expand {
  background: none;
  border: none;
  color: #b0b0b0;
  cursor: pointer;
  padding: 4px;
  transition: transform 0.3s ease;
}

.segment-item.expanded .segment-expand {
  transform: rotate(90deg);
}

.segment-name-input {
  flex: 1;
  background: none;
  border: none;
  color: #ffffff;
  font-size: 1rem;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.segment-name-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.1);
}

.segment-duration {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
}

.segment-duration-input {
  width: 60px;
  background: #3a3a3a;
  border: 1px solid #4a4a4a;
  border-radius: 4px;
  color: #ffffff;
  text-align: center;
  padding: 4px;
  font-size: 0.9rem;
}

.segment-duration-input:focus {
  outline: none;
  border-color: #68d391;
}

.segment-delete {
  background: #e53e3e;
  border: none;
  color: white;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.segment-delete:hover {
  background: #c53030;
}

.segment-details {
  padding: 0 15px 15px;
  display: none;
  gap: 15px;
  flex-direction: column;
}

.segment-item.expanded .segment-details {
  display: flex;
}

.segment-alert-select {
  width: 100%;
  background: #3a3a3a;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  color: #ffffff;
  padding: 8px 12px;
  font-size: 0.9rem;
}

.segment-css-textarea {
  width: 100%;
  min-height: 100px;
  background: #3a3a3a;
  border: 1px solid #4a4a4a;
  border-radius: 6px;
  color: #ffffff;
  padding: 10px 12px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 0.8rem;
  resize: vertical;
}

.segment-css-textarea:focus {
  outline: none;
  border-color: #68d391;
}

/* User profile */
.user-profile {
  text-align: center;
}

.user-avatar-large {
  margin: 0 auto 20px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #4a5568;
}

.user-avatar-large img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Sessions list */
.sessions-actions {
  margin-bottom: 20px;
  text-align: right;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.session-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: #333;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.session-item:hover {
  background: #383838;
}

.session-item.current {
  background: #2d5a2d;
  border: 1px solid #68d391;
}

.session-checkbox {
  width: 16px;
  height: 16px;
}

.session-info {
  flex: 1;
}

.session-item-name {
  font-weight: 500;
  color: #ffffff;
  margin-bottom: 4px;
}

.session-item-id {
  font-size: 0.8rem;
  color: #888;
  font-family: monospace;
}

.session-delete {
  background: #e53e3e;
  border: none;
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.3s ease;
}

.session-delete:hover {
  background: #c53030;
}

/* Responsive design */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    text-align: center;
    padding: 15px 20px;
  }

  .header-left,
  .header-right {
    justify-content: center;
    flex: none;
    min-width: auto;
  }

  .header-center {
    order: -1;
    margin-bottom: 15px;
  }

  .timer-display {
    font-size: 4rem;
  }

  .segment-header {
    flex-wrap: wrap;
    gap: 8px;
  }

  .segment-duration {
    min-width: auto;
  }

  .modal-content {
    width: 95%;
    margin: 10px;
  }

  .timer-controls {
    flex-direction: column;
    gap: 10px;
  }

  .timer-btn {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .timer-display {
    font-size: 3rem;
  }

  .segment-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .header {
    padding: 10px 15px;
  }

  .modal-body {
    padding: 15px;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}

/* Utility classes */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}