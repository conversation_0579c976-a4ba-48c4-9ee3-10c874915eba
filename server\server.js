/**
 * Express + WebSocket server for collaborative timer
 */

const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const dotenv = require('dotenv');
const SessionManager = require('./sessions');

// Load environment variables
dotenv.config();

/**
 * Timer server class
 */
class TimerServer {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = null;
    this.sessionManager = new SessionManager();
    this.port = process.env.PORT || 3000;
    this.wsPort = process.env.WS_PORT || this.port;
    
    this.setupExpress();
    this.setupWebSocket();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup Express middleware and static serving
   */
  setupExpress() {
    // Trust proxy for production deployments
    this.app.set('trust proxy', 1);

    // Security headers
    this.app.use((req, res, next) => {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      next();
    });

    // CORS for development
    if (process.env.NODE_ENV === 'development') {
      this.app.use((req, res, next) => {
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
        res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
        next();
      });
    }

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Serve static files from public directory
    this.app.use(express.static(path.join(__dirname, '../public'), {
      maxAge: '1h',
      etag: true
    }));

    // Request logging in development
    if (process.env.NODE_ENV === 'development') {
      this.app.use((req, res, next) => {
        console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
        next();
      });
    }
  }

  /**
   * Setup WebSocket server
   */
  setupWebSocket() {
    this.wss = new WebSocket.Server({ 
      server: this.server,
      clientTracking: true,
      perMessageDeflate: false
    });

    this.wss.on('connection', (ws, req) => {
      this.handleWebSocketConnection(ws, req);
    });

    this.wss.on('error', (error) => {
      console.error('WebSocket server error:', error);
    });

    // Cleanup interval for dead connections
    setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          console.log('Terminating dead connection');
          return ws.terminate();
        }
        
        ws.isAlive = false;
        ws.ping();
      });
    }, 30000);
  }

  /**
   * Handle new WebSocket connection
   * @param {WebSocket} ws - WebSocket connection
   * @param {http.IncomingMessage} req - HTTP request
   */
  handleWebSocketConnection(ws, req) {
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    console.log(`WebSocket connection from ${clientIp}`);

    // Connection state
    ws.isAlive = true;
    ws.sessionId = null;
    ws.clientId = null;

    // Handle ping/pong for keepalive
    ws.on('pong', () => {
      ws.isAlive = true;
    });

    // Handle messages
    ws.on('message', (message) => {
      this.handleWebSocketMessage(ws, message);
    });

    // Handle connection close
    ws.on('close', (code, reason) => {
      this.handleWebSocketClose(ws, code, reason);
    });

    // Handle connection error
    ws.on('error', (error) => {
      console.error(`WebSocket error for ${clientIp}:`, error);
    });
  }

  /**
   * Handle WebSocket message
   * @param {WebSocket} ws - WebSocket connection
   * @param {Buffer} message - Message buffer
   */
  handleWebSocketMessage(ws, message) {
    try {
      const data = JSON.parse(message.toString());
      
      // Handle ping messages
      if (data.type === 'ping') {
        ws.send(JSON.stringify({ type: 'pong' }));
        return;
      }

      // Validate message structure
      if (!data.type) {
        console.warn('Message without type received');
        return;
      }

      // Route message to session manager
      this.sessionManager.handleMessage(ws, data);
      
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
      this.sendError(ws, 'Invalid message format');
    }
  }

  /**
   * Handle WebSocket connection close
   * @param {WebSocket} ws - WebSocket connection
   * @param {number} code - Close code
   * @param {string} reason - Close reason
   */
  handleWebSocketClose(ws, code, reason) {
    console.log(`WebSocket closed: ${code} ${reason}`);
    
    // Remove from session
    if (ws.sessionId && ws.clientId) {
      this.sessionManager.removeClient(ws.sessionId, ws.clientId);
    }
  }

  /**
   * Setup HTTP routes
   */
  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        sessions: this.sessionManager.getSessionCount(),
        connections: this.wss.clients.size
      });
    });

    // API endpoint for session info (optional)
    this.app.get('/api/session/:sessionId', (req, res) => {
      const { sessionId } = req.params;
      const session = this.sessionManager.getSession(sessionId);
      
      if (!session) {
        return res.status(404).json({ error: 'Session not found' });
      }

      // Return public session info (no sensitive data)
      res.json({
        sessionId,
        name: session.name || 'Pomodoro Session',
        description: session.description || 'Focus and break timer',
        userCount: session.users.size,
        isActive: session.timer.isRunning,
        segmentCount: session.segments.items.length
      });
    });

    // Catch-all route for SPA (single page application)
    this.app.get('/:sessionId?', (req, res, next) => {
      const { sessionId } = req.params;
      
      // Validate session ID format if provided
      if (sessionId && !/^[a-z0-9-]{3,20}$/.test(sessionId)) {
        return res.status(404).send('Invalid session ID format');
      }
      
      // Serve index.html for all routes (SPA routing)
      res.sendFile(path.join(__dirname, '../public/index.html'));
    });

    // 404 handler for API routes
    this.app.use('/api/*', (req, res) => {
      res.status(404).json({ error: 'API endpoint not found' });
    });
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // Global error handler
    this.app.use((error, req, res, next) => {
      console.error('Express error:', error);
      
      if (res.headersSent) {
        return next(error);
      }
      
      res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      this.gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    // Handle unhandled rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.gracefulShutdown('UNHANDLED_REJECTION');
    });

    // Handle termination signals
    process.on('SIGTERM', () => {
      console.log('SIGTERM received');
      this.gracefulShutdown('SIGTERM');
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received');
      this.gracefulShutdown('SIGINT');
    });
  }

  /**
   * Send error message to WebSocket client
   * @param {WebSocket} ws - WebSocket connection
   * @param {string} message - Error message
   */
  sendError(ws, message) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'error',
        message
      }));
    }
  }

  /**
   * Start the server
   */
  start() {
    this.server.listen(this.port, () => {
      console.log(`Timer server running on port ${this.port}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`WebSocket server: ws://localhost:${this.port}`);
      console.log(`Health check: http://localhost:${this.port}/health`);
    });

    this.server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${this.port} is already in use`);
        process.exit(1);
      } else {
        console.error('Server error:', error);
      }
    });
  }

  /**
   * Graceful shutdown
   * @param {string} signal - Shutdown signal
   */
  gracefulShutdown(signal) {
    console.log(`Graceful shutdown initiated by ${signal}`);
    
    // Close WebSocket server
    if (this.wss) {
      console.log('Closing WebSocket server...');
      this.wss.close(() => {
        console.log('WebSocket server closed');
      });
    }

    // Close HTTP server
    this.server.close(() => {
      console.log('HTTP server closed');
      process.exit(0);
    });

    // Force exit after 10 seconds
    setTimeout(() => {
      console.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  }

  /**
   * Get server statistics
   * @returns {Object} Server statistics
   */
  getStats() {
    return {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      sessions: this.sessionManager.getSessionCount(),
      connections: this.wss ? this.wss.clients.size : 0,
      environment: process.env.NODE_ENV || 'development'
    };
  }
}

// Create and start server
const server = new TimerServer();
server.start();

module.exports = TimerServer;