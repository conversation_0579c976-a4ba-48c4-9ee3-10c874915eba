/**
 * Share functionality for session URLs
 */

/**
 * Share manager class
 */
class ShareManager {
  constructor() {
    this.setupEventListeners();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    // Share button
    const shareBtn = Utils.DOM.getId('shareBtn');
    Utils.Events.on(shareBtn, 'click', () => this.shareSession());
  }

  /**
   * Share current session
   */
  async shareSession() {
    const sessionId = window.sessions?.getCurrentSessionId();
    if (!sessionId) {
      alert('No active session to share');
      return;
    }

    const sessionUrl = this.generateSessionUrl(sessionId);
    
    try {
      await this.copyToClipboard(sessionUrl);
      this.showShareSuccess();
    } catch (error) {
      console.warn('Failed to copy to clipboard:', error);
      this.showShareFallback(sessionUrl);
    }
  }

  /**
   * Generate session URL
   * @param {string} sessionId - Session ID
   * @returns {string} Full session URL
   */
  generateSessionUrl(sessionId) {
    const baseUrl = `${window.location.protocol}//${window.location.host}`;
    return `${baseUrl}/${sessionId}`;
  }

  /**
   * Copy text to clipboard using modern API
   * @param {string} text - Text to copy
   * @returns {Promise} Copy promise
   */
  async copyToClipboard(text) {
    // Try modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
      return navigator.clipboard.writeText(text);
    }
    
    // Fallback to legacy method
    return this.legacyCopyToClipboard(text);
  }

  /**
   * Legacy clipboard copy method
   * @param {string} text - Text to copy
   * @returns {Promise} Copy promise
   */
  legacyCopyToClipboard(text) {
    return new Promise((resolve, reject) => {
      // Create temporary textarea
      const textarea = Utils.DOM.create('textarea', {
        value: text,
        style: 'position: fixed; top: -9999px; left: -9999px; opacity: 0;'
      });
      
      document.body.appendChild(textarea);
      
      try {
        // Select and copy text
        textarea.select();
        textarea.setSelectionRange(0, 99999); // For mobile devices
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textarea);
        
        if (successful) {
          resolve();
        } else {
          reject(new Error('Copy command failed'));
        }
      } catch (error) {
        document.body.removeChild(textarea);
        reject(error);
      }
    });
  }

  /**
   * Show success message for share
   */
  showShareSuccess() {
    this.showNotification('Session URL copied to clipboard!', 'success');
  }

  /**
   * Show fallback share modal
   * @param {string} url - URL to share
   */
  showShareFallback(url) {
    const modal = this.createShareModal(url);
    document.body.appendChild(modal);
    Utils.DOM.showModal(modal.id);
  }

  /**
   * Create share fallback modal
   * @param {string} url - URL to share
   * @returns {HTMLElement} Modal element
   */
  createShareModal(url) {
    const modal = Utils.DOM.create('div', {
      id: 'shareFallbackModal',
      className: 'modal show'
    });

    const modalContent = Utils.DOM.create('div', { className: 'modal-content' });
    
    const modalHeader = Utils.DOM.create('div', { className: 'modal-header' });
    const title = Utils.DOM.create('h2', {}, 'Share Session');
    const closeBtn = Utils.DOM.create('button', { 
      className: 'modal-close',
      'data-close': 'shareFallbackModal'
    }, '×');
    
    modalHeader.appendChild(title);
    modalHeader.appendChild(closeBtn);

    const modalBody = Utils.DOM.create('div', { className: 'modal-body' });
    
    const description = Utils.DOM.create('p', {}, 
      'Copy this URL to share the session with others:');
    
    const urlInput = Utils.DOM.create('input', {
      type: 'text',
      className: 'form-control',
      value: url,
      readonly: true,
      style: 'font-family: monospace; font-size: 0.9rem;'
    });

    const buttonContainer = Utils.DOM.create('div', {
      style: 'margin-top: 15px; text-align: right;'
    });

    const selectBtn = Utils.DOM.create('button', {
      className: 'btn btn-secondary',
      style: 'margin-right: 10px;'
    }, 'Select All');

    const closeModalBtn = Utils.DOM.create('button', {
      className: 'btn btn-primary'
    }, 'Close');

    buttonContainer.appendChild(selectBtn);
    buttonContainer.appendChild(closeModalBtn);

    modalBody.appendChild(description);
    modalBody.appendChild(urlInput);
    modalBody.appendChild(buttonContainer);

    modalContent.appendChild(modalHeader);
    modalContent.appendChild(modalBody);
    modal.appendChild(modalContent);

    // Event handlers
    Utils.Events.on(selectBtn, 'click', () => {
      urlInput.select();
      urlInput.setSelectionRange(0, 99999);
    });

    Utils.Events.on(closeBtn, 'click', () => this.closeShareModal(modal));
    Utils.Events.on(closeModalBtn, 'click', () => this.closeShareModal(modal));
    
    Utils.Events.on(modal, 'click', (e) => {
      if (e.target === modal) {
        this.closeShareModal(modal);
      }
    });

    // Auto-select text when modal opens
    setTimeout(() => {
      urlInput.select();
      urlInput.setSelectionRange(0, 99999);
    }, 100);

    return modal;
  }

  /**
   * Close share modal
   * @param {HTMLElement} modal - Modal element to close
   */
  closeShareModal(modal) {
    Utils.DOM.hideModal(modal.id);
    setTimeout(() => {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
    }, 300);
  }

  /**
   * Show notification message
   * @param {string} message - Notification message
   * @param {string} type - Notification type (success, error, info)
   */
  showNotification(message, type = 'info') {
    // Remove existing notification
    const existing = Utils.DOM.getId('shareNotification');
    if (existing) {
      existing.remove();
    }

    const notification = Utils.DOM.create('div', {
      id: 'shareNotification',
      className: `share-notification ${type}`,
      style: `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#48bb78' : type === 'error' ? '#f56565' : '#4a5568'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-size: 0.9rem;
        max-width: 300px;
        animation: slideInRight 0.3s ease;
      `
    }, message);

    // Add CSS animation
    const style = Utils.DOM.create('style');
    style.textContent = `
      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      
      @keyframes slideOutRight {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;
    
    if (!Utils.DOM.getId('shareNotificationStyles')) {
      style.id = 'shareNotificationStyles';
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Auto-remove notification after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  /**
   * Generate QR code for session URL (future enhancement)
   * @param {string} url - URL to encode
   * @returns {string} QR code data URL
   */
  generateQRCode(url) {
    // This could be implemented with a QR code library
    // For now, return placeholder
    return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
  }

  /**
   * Share via Web Share API if available
   * @param {string} sessionId - Session ID to share
   * @returns {Promise} Share promise
   */
  async shareViaWebShareAPI(sessionId) {
    if (!navigator.share) {
      throw new Error('Web Share API not supported');
    }

    const url = this.generateSessionUrl(sessionId);
    const session = window.sessions?.getCurrentSession();
    const title = session ? session.name : 'Pomodoro Session';

    return navigator.share({
      title: title,
      text: 'Join my Pomodoro timer session',
      url: url
    });
  }

  /**
   * Share session with enhanced options
   */
  async shareSessionEnhanced() {
    const sessionId = window.sessions?.getCurrentSessionId();
    if (!sessionId) {
      this.showNotification('No active session to share', 'error');
      return;
    }

    // Try Web Share API first (mobile devices)
    if (navigator.share) {
      try {
        await this.shareViaWebShareAPI(sessionId);
        return;
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.warn('Web Share API failed:', error);
        }
      }
    }

    // Fallback to clipboard
    await this.shareSession();
  }

  /**
   * Get shareable session info
   * @param {string} sessionId - Session ID
   * @returns {Object} Session share info
   */
  getSessionShareInfo(sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    const url = this.generateSessionUrl(sessionId);
    
    return {
      sessionId,
      url,
      name: session?.name || 'Pomodoro Session',
      description: session?.description || 'Focus and break timer',
      segmentCount: session?.segments?.items?.length || 0
    };
  }

  /**
   * Copy session ID only
   * @param {string} sessionId - Session ID to copy
   */
  async copySessionId(sessionId) {
    try {
      await this.copyToClipboard(sessionId);
      this.showNotification('Session ID copied to clipboard!', 'success');
    } catch (error) {
      console.warn('Failed to copy session ID:', error);
      this.showNotification('Failed to copy session ID', 'error');
    }
  }
}

// Create global share manager instance
const shareManager = new ShareManager();

// Export to global scope
window.ShareManager = ShareManager;
window.share = shareManager;