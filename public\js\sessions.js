/**
 * Session management functionality
 */

/**
 * Session manager class
 */
class SessionManager {
  constructor() {
    this.currentSessionId = null;
    this.connectedUsers = new Map();
    
    this.setupEventListeners();
    this.initializeFromUrl();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    // Sessions button
    const sessionsBtn = Utils.DOM.getId('sessionsBtn');
    Utils.Events.on(sessionsBtn, 'click', () => this.showSessionsList());

    // Modal close handlers
    const closeButtons = Utils.DOM.queryAll('.modal-close');
    closeButtons.forEach(btn => {
      Utils.Events.on(btn, 'click', (e) => {
        const modalId = e.target.getAttribute('data-close');
        if (modalId) {
          Utils.DOM.hideModal(modalId);
        }
      });
    });

    // Click outside modal to close
    Utils.Events.on(document, 'click', (e) => {
      if (e.target.classList.contains('modal')) {
        Utils.DOM.hideModal(e.target.id);
      }
    });

    // Clear all sessions button - event listener managed dynamically in updateClearButtonText()

    // WebSocket events
    Utils.Events.on(document, 'websocketConnected', this.handleWebSocketConnected.bind(this));
    Utils.Events.on(document, 'sessionJoined', this.handleSessionJoined.bind(this));
    Utils.Events.on(document, 'sessionCreated', this.handleSessionCreated.bind(this));
    Utils.Events.on(document, 'userConnected', this.handleUserConnected.bind(this));
    Utils.Events.on(document, 'userDisconnected', this.handleUserDisconnected.bind(this));
    Utils.Events.on(document, 'userUpdated', this.handleUserUpdated.bind(this));

    // Browser back/forward
    Utils.Events.on(window, 'popstate', this.handlePopState.bind(this));
  }

  /**
   * Initialize session from URL
   */
  initializeFromUrl() {
    const sessionId = Utils.getSessionIdFromUrl();
    
    if (sessionId) {
      this.joinSession(sessionId);
    } else {
      // No session in URL, create a new one
      this.createNewSession();
    }
  }

  /**
   * Create a new session
   */
  createNewSession() {
    const sessionId = Utils.generateSessionId();
    this.joinSession(sessionId);
  }

  /**
   * Join an existing session or create it if it doesn't exist
   * @param {string} sessionId - Session ID to join
   */
  joinSession(sessionId) {
    if (!Utils.isValidSessionId(sessionId)) {
      console.error('Invalid session ID:', sessionId);
      this.createNewSession();
      return;
    }

    this.currentSessionId = sessionId;
    Utils.setSessionIdInUrl(sessionId);

    // Update session name in header
    this.updateSessionHeader(sessionId);

    // Connect to WebSocket if not already connected
    if (window.ws && !window.ws.isSocketConnected()) {
      window.ws.connect();
    }

    // Join session via WebSocket when connected
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.joinSession(sessionId);
    }

    // Load timer for this session
    if (window.timer) {
      window.timer.loadSession(sessionId);
    }
  }

  /**
   * Update session header information
   * @param {string} sessionId - Session ID
   */
  updateSessionHeader(sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    const sessionName = Utils.DOM.getId('sessionName');
    const sessionDescription = Utils.DOM.getId('sessionDescription');

    if (sessionName) {
      sessionName.textContent = session ? session.name : 'Pomodoro Session';
    }

    if (sessionDescription) {
      sessionDescription.textContent = session ? session.description : 'Focus and break timer';
    }
  }

  /**
   * Show sessions list modal
   */
  showSessionsList() {
    this.renderSessionsList();
    Utils.DOM.showModal('sessionsModal');
  }

  /**
   * Render sessions list in modal
   */
  renderSessionsList() {
    const sessionsList = Utils.DOM.getId('sessionsList');
    const clearAllBtn = Utils.DOM.getId('clearAllSessionsBtn');
    
    if (!sessionsList) return;

    const sessionIds = Utils.Storage.getAllSessionIds();
    
    if (sessionIds.length === 0) {
      sessionsList.innerHTML = '<p class="empty-state">No sessions found</p>';
      if (clearAllBtn) clearAllBtn.style.display = 'none';
      return;
    }

    if (clearAllBtn) clearAllBtn.style.display = 'block';

    sessionsList.innerHTML = '';

    sessionIds.forEach(sessionId => {
      const session = Utils.Storage.getSession(sessionId);
      if (!session) return;

      const sessionItem = this.createSessionItem(sessionId, session);
      sessionsList.appendChild(sessionItem);
    });

    // Initialize clear button text and event listener
    this.updateClearButtonText();
  }

  /**
   * Create session list item element
   * @param {string} sessionId - Session ID
   * @param {Object} session - Session data
   * @returns {HTMLElement} Session item element
   */
  createSessionItem(sessionId, session) {
    const item = Utils.DOM.create('div', {
      className: `session-item ${sessionId === this.currentSessionId ? 'current' : ''}`,
      'data-session-id': sessionId
    });

    const checkbox = Utils.DOM.create('input', {
      type: 'checkbox',
      className: 'session-checkbox'
    });

    const sessionInfo = Utils.DOM.create('div', { className: 'session-info' });
    
    const sessionName = Utils.DOM.create('div', { 
      className: 'session-item-name' 
    }, session.name);
    
    const sessionIdSpan = Utils.DOM.create('div', { 
      className: 'session-item-id' 
    }, sessionId);

    sessionInfo.appendChild(sessionName);
    sessionInfo.appendChild(sessionIdSpan);

    const deleteBtn = Utils.DOM.create('button', {
      className: 'session-delete'
    }, 'Delete');

    item.appendChild(checkbox);
    item.appendChild(sessionInfo);
    item.appendChild(deleteBtn);

    // Event handlers
    Utils.Events.on(sessionInfo, 'click', () => {
      if (sessionId !== this.currentSessionId) {
        this.joinSession(sessionId);
        Utils.DOM.hideModal('sessionsModal');
      }
    });

    Utils.Events.on(checkbox, 'change', () => {
      this.updateClearButtonText();
    });

    Utils.Events.on(deleteBtn, 'click', (e) => {
      e.stopPropagation();
      this.deleteSession(sessionId);
    });

    return item;
  }

  /**
   * Update clear button text based on selected items
   */
  updateClearButtonText() {
    const clearAllBtn = Utils.DOM.getId('clearAllSessionsBtn');
    const checkboxes = Utils.DOM.queryAll('.session-checkbox:checked');
    
    if (clearAllBtn) {
      // Remove existing event listeners
      clearAllBtn.replaceWith(clearAllBtn.cloneNode(true));
      const newClearAllBtn = Utils.DOM.getId('clearAllSessionsBtn');
      
      if (checkboxes.length > 0) {
        newClearAllBtn.textContent = `Delete Selected Sessions (${checkboxes.length})`;
        Utils.Events.on(newClearAllBtn, 'click', () => this.deleteSelectedSessions());
      } else {
        newClearAllBtn.textContent = 'Clear All Sessions';
        Utils.Events.on(newClearAllBtn, 'click', () => this.clearAllSessions());
      }
    }
  }

  /**
   * Delete a specific session
   * @param {string} sessionId - Session ID to delete
   */
  deleteSession(sessionId) {
    if (confirm(`Delete session "${sessionId}"?`)) {
      Utils.Storage.deleteSession(sessionId);
      
      // If deleting current session, create a new one
      if (sessionId === this.currentSessionId) {
        this.createNewSession();
      }
      
      this.renderSessionsList();
    }
  }

  /**
   * Delete selected sessions
   */
  deleteSelectedSessions() {
    const checkboxes = Utils.DOM.queryAll('.session-checkbox:checked');
    const sessionIds = Array.from(checkboxes).map(cb =>
      cb.closest('.session-item').getAttribute('data-session-id')
    ).filter(id => id !== null); // Filter out any null values

    if (sessionIds.length === 0) {
      alert('No sessions selected for deletion');
      return;
    }

    if (confirm(`Delete ${sessionIds.length} selected session(s)?\n\nSessions: ${sessionIds.join(', ')}`)) {
      let needNewSession = false;
      
      sessionIds.forEach(sessionId => {
        if (sessionId) { // Double check sessionId exists
          Utils.Storage.deleteSession(sessionId);
          if (sessionId === this.currentSessionId) {
            needNewSession = true;
          }
        }
      });

      if (needNewSession) {
        this.createNewSession();
      }

      this.renderSessionsList();
    }
  }

  /**
   * Clear all sessions
   */
  clearAllSessions() {
    if (confirm('Delete all sessions? This cannot be undone.')) {
      Utils.Storage.clearAllSessions();
      this.createNewSession();
      this.renderSessionsList();
    }
  }

  /**
   * Update connected users display
   */
  updateConnectedUsersDisplay() {
    const userCount = Utils.DOM.query('.user-count');
    
    if (userCount) {
      const count = this.connectedUsers.size;
      userCount.textContent = `${count} user${count === 1 ? '' : 's'} connected`;
    }
  }

  /**
   * Handle browser back/forward navigation
   * @param {PopStateEvent} event - Pop state event
   */
  handlePopState(event) {
    const sessionId = Utils.getSessionIdFromUrl();
    
    if (sessionId && sessionId !== this.currentSessionId) {
      this.joinSession(sessionId);
    } else if (!sessionId) {
      this.createNewSession();
    }
  }

  // WebSocket event handlers
  handleWebSocketConnected() {
    // Join current session when WebSocket connects
    if (this.currentSessionId && window.ws) {
      window.ws.joinSession(this.currentSessionId);
    }
  }

  handleSessionJoined(event) {
    const { sessionId, session } = event.detail;
    
    // Update session header
    this.updateSessionHeader(sessionId);
    
    // Update connected users from server data
    if (session.users) {
      this.connectedUsers.clear();
      session.users.forEach(user => {
        this.connectedUsers.set(user.clientId, user);
      });
    } else {
      // Fallback: ensure current user is counted
      this.connectedUsers.clear();
      const currentUser = {
        clientId: Utils.Storage.getClientId(),
        name: window.user?.getCurrentUser()?.name || '',
        email: window.user?.getCurrentUser()?.email || '',
        isOnline: true
      };
      this.connectedUsers.set(currentUser.clientId, currentUser);
    }
    this.updateConnectedUsersDisplay();
  }

  handleSessionCreated(event) {
    const { sessionId } = event.detail;
    
    // Update session header
    this.updateSessionHeader(sessionId);
    
    // Add current user to connected users
    this.connectedUsers.clear();
    const currentUser = {
      clientId: Utils.Storage.getClientId(),
      name: window.user?.getCurrentUser()?.name || '',
      email: window.user?.getCurrentUser()?.email || '',
      isOnline: true
    };
    this.connectedUsers.set(currentUser.clientId, currentUser);
    this.updateConnectedUsersDisplay();
  }

  handleUserConnected(event) {
    const { user } = event.detail;
    
    this.connectedUsers.set(user.clientId, user);
    this.updateConnectedUsersDisplay();
    
    console.log('User connected:', user.name || user.clientId);
  }

  handleUserDisconnected(event) {
    const { user } = event.detail;
    
    this.connectedUsers.delete(user.clientId);
    this.updateConnectedUsersDisplay();
    
    console.log('User disconnected:', user.name || user.clientId);
  }

  handleUserUpdated(event) {
    const { user } = event.detail;
    
    this.connectedUsers.set(user.clientId, user);
    this.updateConnectedUsersDisplay();
    
    console.log('User updated:', user.name || user.clientId);
  }

  /**
   * Get current session ID
   * @returns {string|null} Current session ID
   */
  getCurrentSessionId() {
    return this.currentSessionId;
  }

  /**
   * Get current session data
   * @returns {Object|null} Current session data
   */
  getCurrentSession() {
    return this.currentSessionId ? Utils.Storage.getSession(this.currentSessionId) : null;
  }

  /**
   * Get connected users
   * @returns {Map} Map of connected users
   */
  getConnectedUsers() {
    return this.connectedUsers;
  }

  /**
   * Export session configuration
   * @param {string} sessionId - Session ID to export
   * @returns {Object} Session configuration
   */
  exportSessionConfig(sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    if (!session) return null;

    return {
      name: session.name,
      description: session.description,
      segments: session.segments.items.map(segment => ({
        name: segment.name,
        duration: segment.duration,
        alert: segment.alert,
        customCSS: segment.customCSS
      }))
    };
  }

  /**
   * Import session configuration
   * @param {Object} config - Session configuration
   * @param {string} sessionId - Target session ID
   */
  importSessionConfig(config, sessionId) {
    const session = Utils.Storage.getSession(sessionId);
    if (!session) return;

    // Update session with imported config
    if (config.name) session.name = config.name;
    if (config.description) session.description = config.description;
    
    if (config.segments && Array.isArray(config.segments)) {
      session.segments.items = config.segments.map(segment => ({
        name: segment.name || 'Segment',
        duration: parseInt(segment.duration) || 1500,
        alert: segment.alert || 'Default',
        customCSS: segment.customCSS || ''
      }));
      session.segments.lastUpdated = Utils.getCurrentTimestamp();
    }

    // Reset timer to first segment
    session.timer.currentSegment = 0;
    session.timer.timeRemaining = session.segments.items[0]?.duration || 1500;
    session.timer.isRunning = false;
    session.timer.isPaused = false;

    // Save updated session
    Utils.Storage.saveSession(sessionId, session);

    // Update UI
    this.updateSessionHeader(sessionId);
    
    // Sync with server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.updateSegments(sessionId, session.segments);
    }

    // Reload timer
    if (window.timer && sessionId === this.currentSessionId) {
      window.timer.loadSession(sessionId);
    }
  }
}

// Create global session manager instance
const sessionManager = new SessionManager();

// Export to global scope
window.SessionManager = SessionManager;
window.sessions = sessionManager;