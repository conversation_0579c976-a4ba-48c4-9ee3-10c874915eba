{"name": "timeshare", "version": "1.0.0", "description": "A collaborative Pomodoro-like timer application with real-time synchronization", "main": "server/server.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "build": "echo 'No build process needed for vanilla JS'", "test": "echo 'Tests to be implemented'"}, "keywords": ["pomodoro", "timer", "collaborative", "websocket", "real-time"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}