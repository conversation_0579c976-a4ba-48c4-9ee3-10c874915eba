/**
 * Utility functions for the timer application
 */

/**
 * Generate a unique client ID
 * @returns {string} UUID v4 string
 */
function generateClientId() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Generate a unique session ID
 * @returns {string} Session ID string
 */
function generateSessionId() {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Validate session ID format
 * @param {string} sessionId - Session ID to validate
 * @returns {boolean} True if valid
 */
function isValidSessionId(sessionId) {
  return /^[a-z0-9-]{3,20}$/.test(sessionId);
}

/**
 * Validate client ID format
 * @param {string} clientId - Client ID to validate
 * @returns {boolean} True if valid
 */
function isValidClientId(clientId) {
  return /^[a-f0-9-]{36}$/.test(clientId);
}

/**
 * Format time in seconds to MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
function formatTime(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Parse time string to seconds
 * @param {string} timeString - Time in MM:SS format
 * @returns {number} Time in seconds
 */
function parseTime(timeString) {
  const [minutes, seconds] = timeString.split(':').map(Number);
  return (minutes * 60) + seconds;
}

/**
 * Sanitize HTML to prevent XSS
 * @param {string} html - HTML string to sanitize
 * @returns {string} Sanitized HTML
 */
function sanitizeHtml(html) {
  const div = document.createElement('div');
  div.textContent = html;
  return div.innerHTML;
}

/**
 * Debounce function execution
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Get current timestamp
 * @returns {number} Current timestamp in milliseconds
 */
function getCurrentTimestamp() {
  return Date.now();
}

/**
 * Storage utility for LocalStorage operations
 */
const Storage = {
  /**
   * Get client ID from storage or generate new one
   * @returns {string} Client ID
   */
  getClientId() {
    let clientId = localStorage.getItem('client_id');
    if (!clientId || !isValidClientId(clientId)) {
      clientId = generateClientId();
      localStorage.setItem('client_id', clientId);
    }
    return clientId;
  },

  /**
   * Get session data from storage
   * @returns {Object} Session data object
   */
  getSessionData() {
    try {
      const data = localStorage.getItem('session_data');
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Error parsing session data:', error);
      return {};
    }
  },

  /**
   * Save session data to storage
   * @param {Object} sessionData - Session data to save
   */
  saveSessionData(sessionData) {
    try {
      localStorage.setItem('session_data', JSON.stringify(sessionData));
    } catch (error) {
      console.error('Error saving session data:', error);
    }
  },

  /**
   * Get specific session by ID
   * @param {string} sessionId - Session ID
   * @returns {Object|null} Session object or null
   */
  getSession(sessionId) {
    const sessionData = this.getSessionData();
    return sessionData[sessionId] || null;
  },

  /**
   * Save specific session
   * @param {string} sessionId - Session ID
   * @param {Object} session - Session object
   */
  saveSession(sessionId, session) {
    const sessionData = this.getSessionData();
    sessionData[sessionId] = session;
    this.saveSessionData(sessionData);
  },

  /**
   * Delete specific session
   * @param {string} sessionId - Session ID
   */
  deleteSession(sessionId) {
    const sessionData = this.getSessionData();
    delete sessionData[sessionId];
    this.saveSessionData(sessionData);
  },

  /**
   * Get all session IDs
   * @returns {string[]} Array of session IDs
   */
  getAllSessionIds() {
    const sessionData = this.getSessionData();
    return Object.keys(sessionData);
  },

  /**
   * Clear all sessions
   */
  clearAllSessions() {
    localStorage.removeItem('session_data');
  }
};

/**
 * Create default session structure
 * @param {string} sessionId - Session ID
 * @returns {Object} Default session object
 */
function createDefaultSession(sessionId) {
  return {
    name: 'Pomodoro Session',
    description: 'Focus and break timer',
    segments: {
      lastUpdated: getCurrentTimestamp(),
      items: [
        {
          name: 'Focus',
          duration: 1500, // 25 minutes
          alert: 'Default',
          customCSS: ''
        },
        {
          name: 'Break',
          duration: 300, // 5 minutes
          alert: 'Default',
          customCSS: ''
        }
      ]
    },
    timer: {
      repeat: false,
      currentSegment: 0,
      timeRemaining: 1500,
      isRunning: false,
      isPaused: false,
      startedAt: 0,
      startedSegment: 0,
      pausedAt: 0,
      timePaused: 0
    },
    user: {
      name: '',
      email: ''
    }
  };
}

/**
 * Validate session object structure
 * @param {Object} session - Session object to validate
 * @returns {boolean} True if valid
 */
function validateSession(session) {
  if (!session || typeof session !== 'object') return false;
  
  // Check required properties
  const requiredProps = ['name', 'description', 'segments', 'timer', 'user'];
  if (!requiredProps.every(prop => prop in session)) return false;
  
  // Check segments structure
  if (!session.segments.items || !Array.isArray(session.segments.items)) return false;
  if (session.segments.items.length === 0) return false;
  
  // Check each segment
  for (const segment of session.segments.items) {
    if (!segment.name || typeof segment.duration !== 'number' || segment.duration <= 0) {
      return false;
    }
  }
  
  return true;
}

/**
 * Get session ID from URL path
 * @returns {string|null} Session ID or null
 */
function getSessionIdFromUrl() {
  const path = window.location.pathname;
  const match = path.match(/^\/([a-z0-9-]{3,20})$/);
  return match ? match[1] : null;
}

/**
 * Set session ID in URL
 * @param {string} sessionId - Session ID
 */
function setSessionIdInUrl(sessionId) {
  const newUrl = `/${sessionId}`;
  if (window.location.pathname !== newUrl) {
    window.history.pushState({ sessionId }, '', newUrl);
  }
}

/**
 * DOM utility functions
 */
const DOM = {
  /**
   * Get element by ID
   * @param {string} id - Element ID
   * @returns {HTMLElement|null} Element or null
   */
  getId(id) {
    return document.getElementById(id);
  },

  /**
   * Query selector
   * @param {string} selector - CSS selector
   * @returns {HTMLElement|null} Element or null
   */
  query(selector) {
    return document.querySelector(selector);
  },

  /**
   * Query all selectors
   * @param {string} selector - CSS selector
   * @returns {NodeList} NodeList of elements
   */
  queryAll(selector) {
    return document.querySelectorAll(selector);
  },

  /**
   * Create element with attributes
   * @param {string} tag - HTML tag name
   * @param {Object} attributes - Attributes object
   * @param {string} textContent - Text content
   * @returns {HTMLElement} Created element
   */
  create(tag, attributes = {}, textContent = '') {
    const element = document.createElement(tag);
    
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key.startsWith('data-')) {
        element.setAttribute(key, value);
      } else {
        element[key] = value;
      }
    });
    
    if (textContent) {
      element.textContent = textContent;
    }
    
    return element;
  },

  /**
   * Show modal
   * @param {string} modalId - Modal element ID
   */
  showModal(modalId) {
    const modal = this.getId(modalId);
    if (modal) {
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    }
  },

  /**
   * Hide modal
   * @param {string} modalId - Modal element ID
   */
  hideModal(modalId) {
    const modal = this.getId(modalId);
    if (modal) {
      modal.classList.remove('show');
      document.body.style.overflow = '';
    }
  },

  /**
   * Toggle class on element
   * @param {HTMLElement} element - Target element
   * @param {string} className - Class name to toggle
   * @param {boolean} force - Force add/remove
   */
  toggleClass(element, className, force) {
    if (element) {
      element.classList.toggle(className, force);
    }
  }
};

/**
 * Event utility functions
 */
const Events = {
  /**
   * Add event listener
   * @param {HTMLElement|Window|Document} target - Event target
   * @param {string} event - Event name
   * @param {Function} handler - Event handler
   * @param {boolean|Object} options - Event options
   */
  on(target, event, handler, options = false) {
    if (target && target.addEventListener) {
      target.addEventListener(event, handler, options);
    }
  },

  /**
   * Remove event listener
   * @param {HTMLElement|Window|Document} target - Event target
   * @param {string} event - Event name
   * @param {Function} handler - Event handler
   * @param {boolean|Object} options - Event options
   */
  off(target, event, handler, options = false) {
    if (target && target.removeEventListener) {
      target.removeEventListener(event, handler, options);
    }
  },

  /**
   * Dispatch custom event
   * @param {HTMLElement|Window|Document} target - Event target
   * @param {string} eventName - Event name
   * @param {Object} detail - Event detail data
   */
  dispatch(target, eventName, detail = {}) {
    if (target && target.dispatchEvent) {
      const event = new CustomEvent(eventName, { detail });
      target.dispatchEvent(event);
    }
  }
};

// Export utilities to global scope
window.Utils = {
  generateClientId,
  generateSessionId,
  isValidSessionId,
  isValidClientId,
  formatTime,
  parseTime,
  sanitizeHtml,
  debounce,
  getCurrentTimestamp,
  Storage,
  createDefaultSession,
  validateSession,
  getSessionIdFromUrl,
  setSessionIdInUrl,
  DOM,
  Events
};