# Collabrative Pomodoro-like Timer App

This document outlines the specifications for a minimilist application that implements a collabrative Pomodoro-like timer. The app will allow users to set a timer for focus sessions and breaks, with customizable durations and the ability to join sessions created by others.

## Features

- **Basic Timer**: A clean countdown display with start, pause, and reset functionality.
- **Custom Segments**: Users can define unlimited work and break segments, each with customizable durations, names, alert sounds.
- **Session Management**: Users can create and join sessions, with the ability to share session URLs.
- **Real-time Updates**: The timer updates in real-time across all clients in a session, ensuring everyone sees the same countdown.
- **Segments Styling**: Each segment can have custom CSS applied through a basic textarea input which allows styling the entire interface.
- **Minimalist Design**: Focus on simplicity and ease of use.
- **User Management**: Users can set their name and email, which will be used to retrieve their Gravatar profile image.

## Technology Stack

- **Frontend**: Vanilla JavaScript, HTML, CSS
- **Backend**: Node.js + Express + WebSockets for real-time communication.
- **WebSocket**: WebSocket for real-time updates between clients.
- **LocalStorage**: For session data persistence across page reloads.
- **Gravatar**: For user profile images based on email.
- **Schema Validation**: Schema and data validation for messages exchanged between clients and server to ensure data integrity and security.

### Fronted

- **HTML/CSS**: Minimalist design with focus on usability.
- **JavaScript**: Handles timer logic, session management, and real-time updates.
- **WebSocket Client**: Manages real-time communication with the server for session updates.
- **Custom CSS**: Each segment can have its own custom CSS applied through a textarea input, allowing users to style the entire interface.
- **Session Sharing**: Users can share their session URL, allowing others to join and sync the timer.

### Backend

- **Express Server**: Handles HTTP requests and serves the frontend files.
- **WebSocket Server**: Manages real-time communication between clients.
- **Session Management**: In-memory storage for active timer sessions, allowing users to create and join sessions via unique URLs, with future integration with redis or similar for persistence.

## Implementation Plan

### Interface Design

- A series of screenshots or wireframes will be created to visualize the user interface.
- The LLM agent should analyse the screenshots to understand the layout, functionality and style of the application.

### Frontend Interface

- Create a single-page application with a minimalist design.
- Implement the timer display with start, pause, and reset buttons.
- Add functionality to create and join sessions via URL.
  - Use URL path segment to identify sessions, e.g., `https://example.com/session/12345` session ID `12345`.
- Store session data in LocalStorage for persistence across page reloads.
- Implement custom segment management with duration, name, custom CSS and alert sound.
- Implement real-time updates using WebSockets to synchronize the timer across all clients in a session.
- Implement a simple settings panel to configure timer segments, including adding, editing, and deleting segments.
- Implement a button to open the settings panel in a popup modal.
- Implement a share button to copy the session URL to the clipboard.
  - Use modern clipboard API to copy the session URL (`navigator.clipboard.writeText()`).
  - Fallback to legacy method using `document.execCommand('copy')` for older browsers.
  - If the copy operation fails, display a popup modal with the session URL in an input field.
- Implement a "<no> users connected" message to indicate the number of users currently in the session.
- Implement a user button that opens a popup to edit the users name and email.
  - Get the user profile image from Gravatar based on the email (using SHA256 hash).
  - Display the user profile image in the user button.
  - Add a 3px border around the user button that changes color based on the user's online status (Green/Red).
  - The popup should only contain inputs for the user's name and email, with a save button to update the user profile.
- Implement a popup to list all sessions the user has joined, allowing them to switch between sessions.
- Implement export and import segment configurations to JSON, allowing users to save and load their custom segments.
- Alert sounds will be played when a segment ends.
- Toggle a timer-running class on the body element when the timer is running or paused, timer-running when it is running, timer-paused when it is paused, and timer-repeat when repeat mode is enabled.
- In the sessions list, add a checkbox to each item next to the delete button, unchecked by default. When items are selected, change the "Clear All Sessions" to "Delete Selected Sessions" and update the code to remove only the selected items.
- Add a button to the top left corner with a speaker icon, clicking it should unlock the AudioContext. Remove the icon once AudioContext is enabled (you will need to create an event to listen to because clicking any button on the page will enable AudioContext, see the snipped below). Add a alt text to explain that clicking will enabled alerts.

#### Settings Panel

- Implement a settings panel that allows users to configure the following:
  - Edit session name and description.
  - Enable or disable repeat mode for the timer.
  - Add, edit, and delete timer segments.
  - Set custom durations for each segment.
  - Select alert sound for each segment from a dropdown menu.
    - Generate a list of plesent alert sounds using frequency tones.
  - Define custom CSS styles for each segment using a textarea input.
  - Save session configurations to LocalStorage then sync session configurations with the server for real-time updates.
  - Update settings when the input looses focus, allowing users to see changes in real-time (No need for save or edit buttons).

##### Segment List Disign

The segment list will be displayed in a list format with the following layout:

- Row 1
  - Collapse/Expand Button (to show/hide segment details)
  - Segment Name
  - Duration (in minutes, max 1440 minutes)
  - Delete Button (to remove the segment)
    - Display a confirmation dialog before deleting the segment.
- Row 2 (visible when expanded)
  - Alert (dropdown selection)
- Row 3 (visible when expanded)
  - Custom CSS (textarea input)

## Data Models

### Frontend Session Data

The frontend will manage session data in memory and persist it using LocalStorage. Users can create and join sessions, and the session data will be synchronized with the server via WebSockets. The session data will be stored in the browser's LocalStorage to ensure that it persists across page reloads. This means that when a user creates a session or joins an existing one, the session data will be saved locally. A client can be part of multiple sessions, and clients will have a different user profile for each session.

The LocalStorage session data will be structured as follows:

```javascript
const session_data = { 
  "session-id": { // Unique identifier for the session (limited to alphanumeric characters and hyphens, user configurable) (e.g., "session-12345")
    name: "Session Name", // Name of the session
    description: "Session Description", // Description of the session
    segments: { // Segments configuration for the session
      lastUpdated: 1234567890, // Timestamp of the last update. this will be used to determine if the session data is stale.
      items: [{
        alert: "Default", // Alert sound to play when the segment ends
        customCSS: "" // Custom CSS styles for the segment
        duration: 1500, // Duration in seconds (25 minutes)
        name: "Focus", // Name of the segment
      }, {
        alert: "Default", // Alert sound to play when the segment ends
        customCSS: "" // Custom CSS styles for the segment
        duration: 300, // Duration in seconds (5 minutes)
        name: "Break", // Name of the segment
      }]
    },
    timer: { // Timer state for the session
      repeat: false, // Whether the timer should repeat after completing all segments
      currentSegment: 0, // Index of the current segment being timed
      timeRemaining: 1500, // Time remaining in seconds for the current segment
      isRunning: false // Whether the timer is currently running (should be true when the timer is started OR PAUSED, false when stopped)
      isPaused: false // Whether the timer is currently paused
      startedAt: 1234567890 // Timestamp of when the timer was started (the server and clients will calculate and store THEIR OWN startedAt timestamp based on their local time to deal with network latency and clock differences, that means it should never be synced with the server or other clients this also means that the timer can resume correctly if the client is offline for an extended period of time).
      pausedAt: 1234567890 // Timestamp of when the timer was paused (the server and clients will calculate and store THEIR OWN pausedAt timestamp based on their local time to deal with network latency and clock differences, that means it should never be synced with the server or other clients this also means that the timer can resume correctly if the client is offline for an extended period of time).
    },
    user: { // Only store user data for the current client in the session
      name: "User Name", // User's name
      email: "<EMAIL>" // User's emai
    }
  },
  "session-id-2": { // Another session
    ... // Same structure as above
  },
  ... // Additional sessions
}

const client_id = "client-uuid"; // Unique identifier for the client, generated when the user first joins a session
```

Use `session_data` as the key to store the session data in LocalStorage. 

`client_id` is a unique identifier for the client, which is generated when the user first joins a session. This ID is used to identify the client across sessions and is stored in LocalStorage under the key `client_id`. If the client ID is not provided when joining a session, the server will generate a new UUID for the client and the client will store it in LocalStorage.

`client_id` and `session_data` are the only keys used to store data in LocalStorage, and there will be no other data stored in LocalStorage for the application.

DO NOT STORE ANY OTHER DATA IN LOCALSTORAGE.

### Backend Message Structure

The backend will respond with session data in a similar structure to the frontend, but it will also include additional metadata for managing sessions across multiple clients. The backend will handle session creation, joining, and real-time updates via WebSockets.

THE BACKEND DOES NOT CREATE SEGMENT DATA, IT ONLY STORES THE CURRENT STATE OF THE SEGMENTS IT RECEIVES FROM THE CLIENT. The frontend is responsible for managing the segments and their configurations. When a client sends a `join_session` event, and NEW session is created on the server (i.e the session does not exist), the backend will respond with a `session_created` event containing the session ID and an empty session object. The client should send a `segment_update` event with the initial segment data to synchronize the segment data to the server. (The client will generate the initial segment data when it receives the `session_created` event, and store it in LocalStorage then send it to the server).

The backend primarily acts as a relay for real-time updates and does not store any persistent session data. The session data is stored in memory and will be lost when the server restarts. Future implementations may include persistent storage using a database or Redis.

The server is responsible for calculating the time remaining for each segment, the current segment number based on the current time, the time the timer started (the server will need to store the time the timer started in its own state for each session) and weather the timer is running or not. This will ensure that clients that connect late or disconnect and reconnect will see the correct timer state. The server will also handle the logic for repeating segments if the repeat mode is enabled.

The server will also handle the logic for notifying clients when a user connects or disconnects from a session, and will send the updated user list to all clients in the session.

All messages exchanged between the client and server will be validated using a schema to ensure data integrity and security. The schema will define the structure of the messages, including required fields, data types, and validation rules.

#### Backend Message Types

The backend will handle the following message types for session management:

##### join_session

Sent by the client to join an existing session or create a new one.

```javascript
const request = {
  type: 'join_session',
  sessionId: 'session-id', // Unique identifier for the session (only allow alphanumeric characters and hyphens, user configurable)
  clientId: 'client-uuid', // Unique identifier to identify the client (only allow alphanumeric characters and hyphens) (optional)
}
```

The `client-uuid` is used to identify the client across sessions and is stored in LocalStorage under the key `client_id`. If the `client-uuid` is not provided or is invalid, the server will generate a new UUID for the client. The client will store this UUID in LocalStorage for future use using the `client_id` key. The server will respond with either a `session_created` or `session_joined` event based on whether the session already exists or is newly created.

This will update the connected users list in the server session manager, and the server will send a `user_connected` event to all clients in the session to notify them of the new user.

##### session_created

Sent by the server in response to a `join_session` event when a new session is created, containing the session ID and an empty session object.

```javascript
const response = {
  type: 'session_created',
  sessionId: 'session-id',
  session: {} // Empty session object, the client should send a `segment_update` event to upload the segment data.
}
```

##### session_joined

Sent by the server in response to a `join_session` event when the session already exists, containing the session state.

Note: The `startedAt` timestamp is is NOT included in the response, as the client stores thier own `startedAt` timestamp, which is calculated by the client based on their local time. The server calculates the `startedAt` timestamp when the timer is started but does not send it to the client. This is the same for the `pausedAt` timestamp.

```javascript
const response = {
  type: 'session_created',
  sessionId: 'session-id',
  clientId: 'client-uuid',
  session: { // Session data for the joined session
    name: "Session Name",
    description: "Session Description",
    segments: { // Segments configuration for the session
      lastUpdated: 1234567890,
      items: [{
        alert: "Default",
        customCSS: "",
        duration: 1500,
        name: "Focus"
      }, {
        alert: "Default",
        customCSS: "",
        duration: 300,
        name: "Break"
      }]
    },
    timer: { // Timer state for the session
      repeat: false,
      currentSegment: 0,
      timeRemaining: 1500,
      isRunning: false,
      isPaused: false,
      timePaused: 0 // Total time the timer has been paused in seconds across all pauses
    },
    users: [{ // List of users connected to the session
      name: "User Name",
      email: "<EMAIL>",
      clientId: "client-uuid",
      lastSeen: 1234567890,
      isOnline: true
    }]
  }
}
```

##### segments_update

Sent by the client to update the segment configurations (e.g., name, duration, alert sound, custom CSS).

```javascript
const request = {
  type: 'segment_update',
  sessionId: 'session-id',
  segments: {
    lastUpdated: 1234567890, // Timestamp of the last update
    items: [{
      alert: "Default",
      customCSS: "",
      duration: 1500,
      name: "Focus"
    }, {
      alert: "Default",
      customCSS: "",
      duration: 300,
      name: "Break"
    }]
  }
}
```

##### segments_updated

Sent by the server to all clients in a session when the session data is updated, including timer state and segment configurations. Only the segments that have changed will be sent, and the `lastUpdated` timestamp is updated by the client to reflect the time of the last change.

```javascript
const response = {
  type: 'segments_updated',
  sessionId: 'session-id',
  segments: {
    lastUpdated: 1234567890,
    items: [{
      alert: "Default",
      customCSS: "",
      duration: 1500,
      name: "Focus"
    }, {
      alert: "Default",
      customCSS: "",
      duration: 300,
      name: "Break"
    }]
  }
}
```

##### timer_start

Sent by the client to start or resume the timer

Sets the `startedAt` timestamp to the current time.

```javascript
const request = {
  type: 'timer_start',
  sessionId: 'session-id',
}
```

##### timer_pause

Sent by the client to pause the timer.

Sets the `pausedAt` timestamp to the current time.

The server will calculate the time remaining based on the `startedAt` and `pausedAt` timestamps. The server will need a `timePaused` variable to track the total time the timer has been paused, so it can correctly calculate the time remaining when the timer is resumed. This will accumulate the total time the timer has been paused across all pauses.

```javascript
const request = {
  type: 'timer_pause',
  sessionId: 'session-id',
}
```

##### timer_stop

Sent by the client to stop the timer and reset it back to the first segment.

Resets the `startedAt` and `pausedAt` timestamps to `0`. Resets the `currentSegment` to `0`, `timeRemaining` to the duration of the first segment, and sets `isRunning` and `isPaused` to `false`. Also resets the `timePaused` variable to `0`.

```javascript
const request = {
  type: 'timer_stop',
  sessionId: 'session-id',
}
```

##### timer_repeat

Sent by the client to enable or disable repeat mode for the timer.

When repeat mode is enabled, the timer will automatically reset to the first segment after completing all segments and continue from the beginning.

The server will need to calculate the segment index and time remaining based on the current time and the repeat mode setting.

```javascript
const request = {
  type: 'timer_repeat',
  sessionId: 'session-id',
}
```

###### timer_updated

Sent by the server to all clients in a session when the timer state is updated, including the current segment index, the time remaining, whether the timer is running, and whether repeat mode is enabled.

This even is set after the `timer_start`, `timer_pause`, `timer_stop`, and `timer_repeat` events are processed.

Note: The `startedAt` and `pausedAt` timestamps are NOT included in the response, as the client stores their own `startedAt` and `pausedAt` timestamps, which are calculated based on their local time. The server calculates the `startedAt` and `pausedAt` timestamps when the timer is started or paused but does not send them to the client. However, the server will send the `timePaused` variable, which is the total time the timer has been paused in seconds across all pauses. This will be used by the client to calculate the time remaining and segment index when the timer is resumed.

```javascript
const response = {
  type: 'timer_updated',
  sessionId: 'session-id',
  currentSegment: 0,
  timeRemaining: 1500,
  isRunning: false,
  isPaused: false,
  repeat: false, 
  timePaused: 1234567890 // Total time the timer has been paused in seconds across all pauses
}
```

##### user_update

Sent by the client to update the user's name and email in the session.

Responds with a `user_updated` event to notify all clients in the session of the updated user profile information.

```javascript
const request = {
  type: 'user_update',
  sessionId: 'session-id',
  user: {
    name: "New User Name",
    email: "<EMAIL>"
    clientId: "client-uuid", // Unique identifier for the client
  }
}
```

##### user_connected

Sent by the server to notify all clients in a session when a new user connects, including the user's profile information.

```javascript
const response = {
  type: 'user_connected',
  sessionId: 'session-id',
  user: {
    name: "User Name",
    email: "<EMAIL>",
    clientId: "client-uuid", // Unique identifier for the client
    lastSeen: 1234567890,
    isOnline: true
  }
}
```

##### user_disconnected

Sent by the server to notify all clients in a session when a user disconnects, including the user's profile information.

```javascript
const response = {
  type: 'user_connected',
  sessionId: 'session-id',
  user: {
    name: "User Name",
    email: "<EMAIL>",
    clientId: "client-uuid", // Unique identifier for the client
    lastSeen: 1234567890,
    isOnline: false
  }
}
```

##### user_updated

Sent by the server to notify all clients in a session when a user's profile is updated, including the updated user's profile information.

```javascript
const response = {
  type: 'user_updated',
  sessionId: 'session-id',
  user: {
    name: "New User Name",
    email: "<EMAIL>",
    clientId: "client-uuid", // Unique identifier for the client
    lastSeen: 1234567890,
    isOnline: true
  }
}
```

## File Structure

```bash
timer/
├── docs/
│   └── SPECIFICATIONS.md   # This file
├── public/
│   ├── css/                # Stylesheets
│   │   └── styles.css      # Base styles
│   ├── js/                 # JavaScript files
│   │   ├── libs/           # Third-party libraries
│   │   ├── app.js          # Main application logic
│   │   ├── timer.js        # Timer functionality
│   │   ├── settings.js     # Settings panel logic
│   │   ├── share.js        # Share button logic
│   │   ├── user.js         # User identification & profile management
│   │   ├── utils.js        # Utility functions
│   │   ├── sessions.js     # Sessions management logic
│   │   ├── websocket.js    # WebSocket client for real-time updates
│   │   └── alerts.js       # Audio notification system
│   ├── index.html          # Main HTML file
│   └── favicon.ico         # Favicon for the application
├── server/
│   ├── .env                # Environment variables
│   ├── .env.local          # Local environment variables
│   ├── .env.production     # Production environment variables
│   ├── .env.example        # Example environment variables
│   ├── server.js           # Express + WebSocket server
│   └── sessions.js         # Session management
├── tests/                  # Unit and integration tests (empty for now)
├── .gitignore              # Git ignore file
├── .prettierrc.json        # Prettier configuration
├── LICENSE                 # Project license (MIT License)
├── package.json            # Project metadata and dependencies
├── package-lock.json       # Dependency lock file
└── README.md               # Project readme
```

## Coding Standards

- DO NOT use any client third-party libraries or frameworks (e.g., React, Vue, Angular) to keep the application lightweight and focused on the core functionality.
- DO NOT create any migration code when refactoring the codebase.
- Use ES6+ syntax for JavaScript.
- Follow consistent naming conventions (camelCase for variables and functions).
- Use template literals for string interpolation.
- Use arrow functions for concise function expressions.
- Use `const` and `let` for variable declarations, avoiding `var`.
- Use strict equality (`===`) for comparisons.
- Use concise and meaningful variable and function names.
- Use modular code structure, separating concerns into different files (e.g., timer logic, settings, user management).
- Use JSDoc for function documentation to ensure clarity on parameters and return types.
    - Keep comments to a minimum
    - Only use comments in code to explain complex logic or non-obvious code.
- Use async/await for asynchronous operations.
- Use error handling with try/catch blocks for asynchronous operations.
- Use Prettier for code formatting to maintain consistent style across the codebase.

## Command Line Interface (CLI)

LLM agents should use the following CLI commands to manage the application:

- `npm start`: Start the development server.
- `npm run build`: Build the application for production.
- `npm test`: Run tests (currently empty).

Always change to the root directory of the project before executing these commands.

## Notes

### Unlock on Any User Input

In modern browsers, the AudioContext must be unlocked by user interaction (e.g., a click, key press, or touch event) before it can be used to play audio. This is a security measure to prevent autoplaying audio without user consent.

To ensure the AudioContext is unlocked, you can add event listeners for user interactions and resume the AudioContext when any of these events occur. Here’s an example of how to implement this:

This code snippet should be adapted to the codebase.

```js
const ctx = new AudioContext();

function unlockAudio() {
  if (ctx.state === 'suspended') {
    ctx.resume().then(() => {
      console.log('AudioContext resumed');
      // Now safe to play audio
    });
  }

  // Only run this once
  window.removeEventListener('click', unlockAudio);
  window.removeEventListener('keydown', unlockAudio);
  window.removeEventListener('touchstart', unlockAudio);
}

window.addEventListener('click', unlockAudio);
window.addEventListener('keydown', unlockAudio);
window.addEventListener('touchstart', unlockAudio);
```