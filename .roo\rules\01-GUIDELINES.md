# Agent Guidelines

Guidelines for coding agents to follow when implementing new features or fixing bugs.

## Coding Standards

- DO NOT use any client third-party libraries or frameworks (e.g., React, Vue, Angular) to keep the application lightweight and focused on the core functionality.
- DO NOT create any migration code when refactoring the codebase.
- Use ES6+ syntax for JavaScript.
- Follow consistent naming conventions (camelCase for variables and functions).
- Use template literals for string interpolation.
- Use arrow functions for concise function expressions.
- Use `const` and `let` for variable declarations, avoiding `var`.
- Use strict equality (`===`) for comparisons.
- Use concise and meaningful variable and function names.
- Use modular code structure, separating concerns into different files (e.g., timer logic, settings, user management).
- Use JSDoc for function documentation to ensure clarity on parameters and return types.
    - Keep comments to a minimum
    - Only use comments in code to explain complex logic or non-obvious code.
- Use async/await for asynchronous operations.
- Use error handling with try/catch blocks for asynchronous operations.
- Use Prettier for code formatting to maintain consistent style across the codebase.

## Command Line Interface (CLI)

LLM agents should use the following CLI commands to manage the application:

- `npm start`: Start the development server.
- `npm run build`: Build the application for production.
- `npm test`: Run tests (currently empty).

Always change to the root directory of the project before executing these commands.
