/**
 * User management functionality
 */

/**
 * User manager class
 */
class UserManager {
  constructor() {
    this.currentUser = {
      name: '',
      email: '',
      clientId: Utils.Storage.getClientId()
    };
    
    this.setupEventListeners();
    this.loadUserProfile();
    this.updateUserDisplay();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    // User button to open modal
    const userBtn = Utils.DOM.getId('userBtn');
    Utils.Events.on(userBtn, 'click', () => this.showUserModal());

    // User form inputs
    const userNameInput = Utils.DOM.getId('userNameInput');
    const userEmailInput = Utils.DOM.getId('userEmailInput');

    if (userNameInput) {
      Utils.Events.on(userNameInput, 'blur', this.saveUserProfile.bind(this));
      Utils.Events.on(userNameInput, 'keypress', (e) => {
        if (e.key === 'Enter') {
          e.target.blur();
        }
      });
    }

    if (userEmailInput) {
      Utils.Events.on(userEmailInput, 'blur', this.saveUserProfile.bind(this));
      Utils.Events.on(userEmailInput, 'keypress', (e) => {
        if (e.key === 'Enter') {
          e.target.blur();
        }
      });
    }

    // Session events
    Utils.Events.on(document, 'sessionJoined', this.handleSessionJoined.bind(this));
    Utils.Events.on(document, 'sessionCreated', this.handleSessionCreated.bind(this));
  }

  /**
   * Load user profile for current session
   */
  loadUserProfile() {
    const sessionId = window.sessions?.getCurrentSessionId();
    if (!sessionId) return;

    const session = Utils.Storage.getSession(sessionId);
    if (session && session.user) {
      this.currentUser.name = session.user.name || '';
      this.currentUser.email = session.user.email || '';
    }
  }

  /**
   * Save user profile
   */
  saveUserProfile() {
    const userNameInput = Utils.DOM.getId('userNameInput');
    const userEmailInput = Utils.DOM.getId('userEmailInput');

    if (!userNameInput || !userEmailInput) return;

    const newName = userNameInput.value.trim();
    const newEmail = userEmailInput.value.trim();

    // Validate email if provided
    if (newEmail && !this.isValidEmail(newEmail)) {
      alert('Please enter a valid email address');
      userEmailInput.focus();
      return;
    }

    const hasChanged = newName !== this.currentUser.name || newEmail !== this.currentUser.email;

    if (hasChanged) {
      this.currentUser.name = newName;
      this.currentUser.email = newEmail;

      // Save to current session
      this.saveToCurrentSession();

      // Update UI
      this.updateUserDisplay();

      // Send to server
      this.syncWithServer();
    }
  }

  /**
   * Save user data to current session storage
   */
  saveToCurrentSession() {
    const sessionId = window.sessions?.getCurrentSessionId();
    if (!sessionId) return;

    const session = Utils.Storage.getSession(sessionId);
    if (session) {
      session.user = {
        name: this.currentUser.name,
        email: this.currentUser.email
      };
      Utils.Storage.saveSession(sessionId, session);
    }
  }

  /**
   * Sync user data with server
   */
  syncWithServer() {
    const sessionId = window.sessions?.getCurrentSessionId();
    if (!sessionId || !window.ws || !window.ws.isSocketConnected()) {
      return;
    }

    window.ws.updateUser(sessionId, {
      name: this.currentUser.name,
      email: this.currentUser.email,
      clientId: this.currentUser.clientId
    });
  }

  /**
   * Show user profile modal
   */
  showUserModal() {
    this.populateUserModal();
    Utils.DOM.showModal('userModal');
  }

  /**
   * Populate user modal with current data
   */
  populateUserModal() {
    const userNameInput = Utils.DOM.getId('userNameInput');
    const userEmailInput = Utils.DOM.getId('userEmailInput');
    const userAvatarLarge = Utils.DOM.getId('userAvatarLarge');

    if (userNameInput) {
      userNameInput.value = this.currentUser.name;
    }

    if (userEmailInput) {
      userEmailInput.value = this.currentUser.email;
    }

    if (userAvatarLarge) {
      userAvatarLarge.src = this.getGravatarUrl(this.currentUser.email, 160);
    }
  }

  /**
   * Update user display in header
   */
  updateUserDisplay() {
    const userAvatar = Utils.DOM.getId('userAvatar');
    const userBtn = Utils.DOM.getId('userBtn');

    if (userAvatar) {
      userAvatar.src = this.getGravatarUrl(this.currentUser.email, 80);
      userAvatar.alt = this.currentUser.name || 'User avatar';
    }

    if (userBtn) {
      const title = this.currentUser.name ? 
        `${this.currentUser.name} - Edit profile` : 
        'Edit profile';
      userBtn.title = title;
      
      // Update online status
      userBtn.classList.add('online'); // Always online for current user
    }
  }

  /**
   * Generate Gravatar URL
   * @param {string} email - User email
   * @param {number} size - Image size
   * @returns {string} Gravatar URL
   */
  getGravatarUrl(email, size = 80) {
    if (!email) {
      // Return default avatar for empty email
      return `https://www.gravatar.com/avatar/?s=${size}&d=identicon&r=pg`;
    }

    // Generate SHA256 hash of email
    const hash = this.sha256(email.toLowerCase().trim());
    return `https://www.gravatar.com/avatar/${hash}?s=${size}&d=identicon&r=pg`;
  }

  /**
   * Generate SHA256 hash
   * @param {string} message - Message to hash
   * @returns {string} SHA256 hash
   */
  sha256(message) {
    // Simple SHA256 implementation for client-side use
    // Note: This is a basic implementation. For production, consider using crypto.subtle.digest
    return this.simpleSHA256(message);
  }

  /**
   * Simple SHA256 implementation
   * @param {string} ascii - ASCII string to hash
   * @returns {string} Hexadecimal hash
   */
  simpleSHA256(ascii) {
    function rightRotate(value, amount) {
      return (value >>> amount) | (value << (32 - amount));
    }

    function mathPow(x, y) {
      return Math.pow(x, y);
    }

    function isqrt(x) {
      return Math.floor(Math.sqrt(x));
    }

    const maxWord = mathPow(2, 32);
    const length = ascii.length * 8;
    const message = ascii + '\x80';
    const l = message.length / 4 + 2;
    const N = Math.ceil(l / 16);
    const M = new Array(N * 16);

    for (let i = 0; i < N * 16; i++) {
      M[i] = 0;
    }

    for (let i = 0; i < message.length; i++) {
      M[i >> 2] |= message.charCodeAt(i) << ((3 - i) % 4) * 8;
    }

    M[l - 1] = length;

    const K = [
      0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
      0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
      0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
      0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
      0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
      0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
      0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
      0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
    ];

    let H = [
      0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a, 0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
    ];

    const W = new Array(64);

    for (let i = 0; i < N; i++) {
      for (let t = 0; t < 16; t++) {
        W[t] = M[i * 16 + t];
      }

      for (let t = 16; t < 64; t++) {
        W[t] = (rightRotate(W[t - 2], 17) ^ rightRotate(W[t - 2], 19) ^ (W[t - 2] >>> 10)) + W[t - 7] +
               (rightRotate(W[t - 15], 7) ^ rightRotate(W[t - 15], 18) ^ (W[t - 15] >>> 3)) + W[t - 16];
      }

      let a = H[0], b = H[1], c = H[2], d = H[3], e = H[4], f = H[5], g = H[6], h = H[7];

      for (let t = 0; t < 64; t++) {
        const T1 = h + (rightRotate(e, 6) ^ rightRotate(e, 11) ^ rightRotate(e, 25)) + ((e & f) ^ (~e & g)) + K[t] + W[t];
        const T2 = (rightRotate(a, 2) ^ rightRotate(a, 13) ^ rightRotate(a, 22)) + ((a & b) ^ (a & c) ^ (b & c));
        h = g;
        g = f;
        f = e;
        e = (d + T1) % maxWord;
        d = c;
        c = b;
        b = a;
        a = (T1 + T2) % maxWord;
      }

      H = [
        (H[0] + a) % maxWord, (H[1] + b) % maxWord, (H[2] + c) % maxWord, (H[3] + d) % maxWord,
        (H[4] + e) % maxWord, (H[5] + f) % maxWord, (H[6] + g) % maxWord, (H[7] + h) % maxWord
      ];
    }

    return H.map(h => {
      const hex = h.toString(16);
      return '00000000'.substr(0, 8 - hex.length) + hex;
    }).join('');
  }

  /**
   * Validate email format
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Get current user data
   * @returns {Object} Current user data
   */
  getCurrentUser() {
    return { ...this.currentUser };
  }

  /**
   * Get user display name
   * @returns {string} Display name or fallback
   */
  getDisplayName() {
    return this.currentUser.name || this.currentUser.email || 'Anonymous';
  }

  /**
   * Check if user profile is complete
   * @returns {boolean} True if name and email are set
   */
  isProfileComplete() {
    return !!(this.currentUser.name && this.currentUser.email);
  }

  // Session event handlers
  handleSessionJoined(event) {
    this.loadUserProfile();
    this.updateUserDisplay();
  }

  handleSessionCreated(event) {
    this.loadUserProfile();
    this.updateUserDisplay();
  }

  /**
   * Reset user profile for new session
   */
  resetProfile() {
    this.currentUser.name = '';
    this.currentUser.email = '';
    this.updateUserDisplay();
  }

  /**
   * Set user profile data
   * @param {Object} userData - User data object
   */
  setProfile(userData) {
    if (userData.name !== undefined) {
      this.currentUser.name = userData.name;
    }
    if (userData.email !== undefined) {
      this.currentUser.email = userData.email;
    }
    
    this.saveToCurrentSession();
    this.updateUserDisplay();
    this.syncWithServer();
  }
}

// Create global user manager instance
const userManager = new UserManager();

// Export to global scope
window.UserManager = UserManager;
window.user = userManager;