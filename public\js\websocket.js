/**
 * WebSocket client for real-time communication with the server
 */

/**
 * WebSocket client manager
 */
class WebSocketClient {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.messageQueue = [];
    this.messageHandlers = new Map();
    this.heartbeatInterval = null;
    this.heartbeatTimeout = null;
    
    this.setupMessageHandlers();
    this.updateConnectionStatus('disconnected');
  }

  /**
   * Setup default message handlers
   */
  setupMessageHandlers() {
    this.onMessage('session_created', this.handleSessionCreated.bind(this));
    this.onMessage('session_joined', this.handleSessionJoined.bind(this));
    this.onMessage('segments_updated', this.handleSegmentsUpdated.bind(this));
    this.onMessage('timer_updated', this.handleTimerUpdated.bind(this));
    this.onMessage('user_connected', this.handleUserConnected.bind(this));
    this.onMessage('user_disconnected', this.handleUserDisconnected.bind(this));
    this.onMessage('user_updated', this.handleUserUpdated.bind(this));
  }

  /**
   * Connect to WebSocket server
   */
  connect() {
    if (this.isConnected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    this.updateConnectionStatus('connecting');

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.hostname;
    const port = window.location.port || (protocol === 'wss:' ? 443 : 80);
    const wsUrl = `${protocol}//${host}:${port === '80' || port === '443' ? '' : port}`;

    try {
      this.ws = new WebSocket(wsUrl);
      this.setupWebSocketHandlers();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.handleConnectionError();
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  setupWebSocketHandlers() {
    this.ws.onopen = () => {
      this.isConnected = true;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      this.updateConnectionStatus('connected');
      this.startHeartbeat();
      this.processMessageQueue();
      
      console.log('WebSocket connected');
      Utils.Events.dispatch(document, 'websocketConnected');
    };

    this.ws.onclose = (event) => {
      this.handleDisconnection(event);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.handleConnectionError();
    };

    this.ws.onmessage = (event) => {
      this.handleMessage(event);
    };
  }

  /**
   * Handle WebSocket disconnection
   * @param {CloseEvent} event - Close event
   */
  handleDisconnection(event) {
    this.isConnected = false;
    this.isConnecting = false;
    this.stopHeartbeat();
    
    console.log('WebSocket disconnected:', event.code, event.reason);
    Utils.Events.dispatch(document, 'websocketDisconnected');

    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    } else {
      this.updateConnectionStatus('disconnected');
    }
  }

  /**
   * Handle connection error
   */
  handleConnectionError() {
    this.isConnecting = false;
    this.updateConnectionStatus('error');
    
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.scheduleReconnect();
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    this.reconnectAttempts++;
    this.updateConnectionStatus('reconnecting');
    
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    setTimeout(() => {
      if (!this.isConnected) {
        console.log(`Reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        this.connect();
      }
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' });
        
        // Set timeout for pong response
        this.heartbeatTimeout = setTimeout(() => {
          console.warn('Heartbeat timeout, closing connection');
          this.ws.close();
        }, 5000);
      }
    }, 30000);
  }

  /**
   * Stop heartbeat
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout);
      this.heartbeatTimeout = null;
    }
  }

  /**
   * Handle incoming WebSocket message
   * @param {MessageEvent} event - Message event
   */
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data);
      
      // Handle pong response
      if (message.type === 'pong') {
        if (this.heartbeatTimeout) {
          clearTimeout(this.heartbeatTimeout);
          this.heartbeatTimeout = null;
        }
        return;
      }

      // Call registered message handler
      const handler = this.messageHandlers.get(message.type);
      if (handler) {
        handler(message);
      } else {
        console.warn('No handler for message type:', message.type);
      }
      
      // Dispatch generic message event
      Utils.Events.dispatch(document, 'websocketMessage', { message });
      
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  /**
   * Send message to server
   * @param {Object} message - Message object
   */
  send(message) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        this.messageQueue.push(message);
      }
    } else {
      // Queue message for later sending
      this.messageQueue.push(message);
    }
  }

  /**
   * Process queued messages
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }

  /**
   * Register message handler
   * @param {string} messageType - Message type
   * @param {Function} handler - Handler function
   */
  onMessage(messageType, handler) {
    this.messageHandlers.set(messageType, handler);
  }

  /**
   * Unregister message handler
   * @param {string} messageType - Message type
   */
  offMessage(messageType) {
    this.messageHandlers.delete(messageType);
  }

  /**
   * Update connection status UI
   * @param {string} status - Connection status
   */
  updateConnectionStatus(status) {
    const statusElement = Utils.DOM.getId('connectionStatus');
    const statusText = Utils.DOM.query('.status-text');
    
    if (!statusElement || !statusText) return;

    // Remove all status classes
    statusElement.classList.remove('connected', 'connecting', 'reconnecting', 'error');
    
    switch (status) {
      case 'connected':
        statusElement.classList.add('connected');
        statusText.textContent = 'Connected';
        break;
      case 'connecting':
        statusElement.classList.add('connecting');
        statusText.textContent = 'Connecting...';
        break;
      case 'reconnecting':
        statusElement.classList.add('connecting');
        statusText.textContent = `Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`;
        break;
      case 'error':
        statusElement.classList.add('error');
        statusText.textContent = 'Connection failed';
        break;
      default:
        statusText.textContent = 'Disconnected';
        break;
    }
  }

  /**
   * Join or create a session
   * @param {string} sessionId - Session ID
   */
  joinSession(sessionId) {
    const clientId = Utils.Storage.getClientId();
    this.send({
      type: 'join_session',
      sessionId,
      clientId
    });
  }

  /**
   * Update segments data
   * @param {string} sessionId - Session ID
   * @param {Object} segments - Segments data
   */
  updateSegments(sessionId, segments) {
    this.send({
      type: 'segments_update',
      sessionId,
      segments
    });
  }

  /**
   * Start timer
   * @param {string} sessionId - Session ID
   */
  startTimer(sessionId) {
    this.send({
      type: 'timer_start',
      sessionId
    });
  }

  /**
   * Pause timer
   * @param {string} sessionId - Session ID
   */
  pauseTimer(sessionId) {
    this.send({
      type: 'timer_pause',
      sessionId
    });
  }

  /**
   * Stop timer
   * @param {string} sessionId - Session ID
   */
  stopTimer(sessionId) {
    this.send({
      type: 'timer_stop',
      sessionId
    });
  }

  /**
   * Toggle repeat mode
   * @param {string} sessionId - Session ID
   */
  toggleRepeat(sessionId) {
    this.send({
      type: 'timer_repeat',
      sessionId
    });
  }

  /**
   * Move to next segment
   * @param {string} sessionId - Session ID
   */
  nextSegment(sessionId) {
    this.send({
      type: 'timer_next_segment',
      sessionId
    });
  }

  /**
   * Update user profile
   * @param {string} sessionId - Session ID
   * @param {Object} user - User data
   */
  updateUser(sessionId, user) {
    this.send({
      type: 'user_update',
      sessionId,
      user
    });
  }

  // Message handlers
  handleSessionCreated(message) {
    Utils.Events.dispatch(document, 'sessionCreated', { 
      sessionId: message.sessionId,
      session: message.session 
    });
  }

  handleSessionJoined(message) {
    Utils.Events.dispatch(document, 'sessionJoined', { 
      sessionId: message.sessionId,
      clientId: message.clientId,
      session: message.session 
    });
  }

  handleSegmentsUpdated(message) {
    Utils.Events.dispatch(document, 'segmentsUpdated', { 
      sessionId: message.sessionId,
      segments: message.segments 
    });
  }

  handleTimerUpdated(message) {
    Utils.Events.dispatch(document, 'timerUpdated', { 
      sessionId: message.sessionId,
      currentSegment: message.currentSegment,
      timeRemaining: message.timeRemaining,
      isRunning: message.isRunning,
      isPaused: message.isPaused,
      repeat: message.repeat,
      timePaused: message.timePaused
    });
  }

  handleUserConnected(message) {
    Utils.Events.dispatch(document, 'userConnected', { 
      sessionId: message.sessionId,
      user: message.user 
    });
  }

  handleUserDisconnected(message) {
    Utils.Events.dispatch(document, 'userDisconnected', { 
      sessionId: message.sessionId,
      user: message.user 
    });
  }

  handleUserUpdated(message) {
    Utils.Events.dispatch(document, 'userUpdated', { 
      sessionId: message.sessionId,
      user: message.user 
    });
  }

  /**
   * Disconnect WebSocket
   */
  disconnect() {
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnecting');
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
    this.messageQueue = [];
    this.updateConnectionStatus('disconnected');
  }

  /**
   * Get connection state
   * @returns {boolean} True if connected
   */
  isSocketConnected() {
    return this.isConnected;
  }
}

// Create global WebSocket client instance
const wsClient = new WebSocketClient();

// Export to global scope
window.WebSocketClient = WebSocketClient;
window.ws = wsClient;