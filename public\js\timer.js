/**
 * Timer functionality for the Pomodoro application
 */

/**
 * Timer manager class
 */
class Timer {
  constructor() {
    this.currentSession = null;
    this.currentSessionId = null;
    this.intervalId = null;
    
    this.setupEventListeners();
    this.updateDisplay();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    // Timer control buttons
    const startBtn = Utils.DOM.getId('startBtn');
    const pauseBtn = Utils.DOM.getId('pauseBtn');
    const stopBtn = Utils.DOM.getId('stopBtn');
    const nextBtn = Utils.DOM.getId('nextBtn');

    Utils.Events.on(startBtn, 'click', () => this.start());
    Utils.Events.on(pauseBtn, 'click', () => this.pause());
    Utils.Events.on(stopBtn, 'click', () => this.stop());
    Utils.Events.on(nextBtn, 'click', () => this.nextSegment());

    // WebSocket events
    Utils.Events.on(document, 'sessionJoined', this.handleSessionJoined.bind(this));
    Utils.Events.on(document, 'sessionCreated', this.handleSessionCreated.bind(this));
    Utils.Events.on(document, 'timerUpdated', this.handleTimerUpdated.bind(this));
    Utils.Events.on(document, 'segmentsUpdated', this.handleSegmentsUpdated.bind(this));
  }

  /**
   * Load session data and initialize timer
   * @param {string} sessionId - Session ID
   */
  loadSession(sessionId) {
    this.currentSessionId = sessionId;
    this.currentSession = Utils.Storage.getSession(sessionId);
    
    if (!this.currentSession) {
      // Create default session if it doesn't exist
      this.currentSession = Utils.createDefaultSession(sessionId);
      Utils.Storage.saveSession(sessionId, this.currentSession);
    }

    // Calculate current timer state from stored timestamps
    this.sync();

    this.updateDisplay();
    this.updateTimerState();
  }

  /**
   * Start the timer
   */
  start() {
    if (!this.currentSessionId || !this.currentSession) {
      console.warn('No active session to start timer');
      return;
    }

    // Update local state
    if (!this.currentSession.timer.isRunning) {
      // Starting fresh
      this.currentSession.timer.startedAt = Utils.getCurrentTimestamp();
      this.currentSession.timer.startedSegment = this.currentSession.timer.currentSegment;
      this.currentSession.timer.timePaused = 0;
    } else if (this.currentSession.timer.isPaused && this.currentSession.timer.pausedAt > 0) {
      // Resuming from pause
      this.currentSession.timer.timePaused += Utils.getCurrentTimestamp() - this.currentSession.timer.pausedAt;
    }

    this.currentSession.timer.isRunning = true;
    this.currentSession.timer.isPaused = false;
    this.currentSession.timer.pausedAt = 0;

    // Save to storage
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);

    // Send to server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.startTimer(this.currentSessionId);
    }

    this.startLocalTimer();
    this.updateDisplay();
    this.updateTimerState();
  }

  /**
   * Pause the timer
   */
  pause() {
    if (!this.currentSessionId || !this.currentSession) {
      console.warn('No active session to pause timer');
      return;
    }

    // Update local state
    this.currentSession.timer.isRunning = true; // Still running but paused
    this.currentSession.timer.isPaused = true;
    this.currentSession.timer.pausedAt = Utils.getCurrentTimestamp();

    // Save to storage
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);

    // Send to server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.pauseTimer(this.currentSessionId);
    }

    this.stopLocalTimer();
    this.updateDisplay();
    this.updateTimerState();
  }

  /**
   * Stop the timer and reset
   */
  stop() {
    if (!this.currentSessionId || !this.currentSession) {
      console.warn('No active session to stop timer');
      return;
    }

    // Reset timer state
    this.currentSession.timer.isRunning = false;
    this.currentSession.timer.isPaused = false;
    this.currentSession.timer.currentSegment = 0;
    this.currentSession.timer.timeRemaining = this.getCurrentSegmentDuration();
    this.resetTiming();

    // Save to storage
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);

    // Send to server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.stopTimer(this.currentSessionId);
    }

    this.stopLocalTimer();
    this.updateDisplay();
    this.updateTimerState();
  }

  /**
   * Move to next segment
   */
  nextSegment() {
    if (!this.currentSessionId || !this.currentSession) {
      console.warn('No active session to move to next segment');
      return;
    }

    const nextSegmentIndex = this.currentSession.timer.currentSegment + 1;
    const totalSegments = this.currentSession.segments.items.length;

    if (nextSegmentIndex >= totalSegments) {
      // Reset to first segment if at the end
      this.currentSession.timer.currentSegment = 0;
    } else {
      // Move to next segment
      this.currentSession.timer.currentSegment = nextSegmentIndex;
    }

    // Reset timer for new segment
    this.reset();

    // If timer was running, keep it running
    if (this.currentSession.timer.isRunning && !this.currentSession.timer.isPaused) {
      this.currentSession.timer.startedAt = Utils.getCurrentTimestamp();
      this.currentSession.timer.startedSegment = this.currentSession.timer.currentSegment;
    }

    // Save to storage
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);

    // Send to server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.nextSegment(this.currentSessionId);
    }

    this.updateDisplay();
    this.updateTimerState();
  }

  /**
   * Start local timer interval
   */
  startLocalTimer() {
    this.stopLocalTimer();
    
    this.intervalId = setInterval(() => {
      this.updateTimerTick();
    }, 1000); // Update every 1 second
  }

  /**
   * Stop local timer interval
   */
  stopLocalTimer() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Reset all timing values (when timer is fully stopped)
   */
  resetTiming() {
    if (!this.currentSession) return;

    this.currentSession.timer.startedAt = 0;
    this.currentSession.timer.startedSegment = 0;
    this.currentSession.timer.pausedAt = 0;
    this.currentSession.timer.timePaused = 0;
  }

  /**
   * Update timer on each tick
   */
  updateTimerTick() {
    if (!this.currentSession || !this.currentSession.timer.isRunning || this.currentSession.timer.isPaused) {
      return;
    }

    // Calculate actual time remaining based on timestamps
    this.sync();

    // Check if segment is complete
    if (this.currentSession.timer.timeRemaining <= 0) {
      this.completeSegment();
      return;
    }

    // Save state and update display
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);
    this.updateDisplay();
  }

  /**
   * Complete current segment and move to next
   */
  async completeSegment() {
    const currentSegment = this.getCurrentSegment();
    if (!currentSegment) return;

    // Play alert sound
    if (window.alerts && currentSegment.alert) {
      try {
        await window.alerts.playAlert(currentSegment.alert);
      } catch (error) {
        console.warn('Failed to play alert:', error);
      }
    }

    // Move to next segment
    const nextSegmentIndex = this.currentSession.timer.currentSegment + 1;
    const totalSegments = this.currentSession.segments.items.length;

    if (nextSegmentIndex >= totalSegments) {
      // All segments complete
      if (this.currentSession.timer.repeat) {
        // Restart from first segment
        this.currentSession.timer.currentSegment = 0;
        this.reset();
      } else {
        // Stop timer
        this.stop();
        return;
      }
    } else {
      // Move to next segment
      this.currentSession.timer.currentSegment = nextSegmentIndex;
      this.reset();
    }

    // Save state and update display
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);
    this.updateDisplay();
  }

  /**
   * Reset timer for new segment
   */
  reset() {
    const newSegmentDuration = this.getCurrentSegmentDuration();
    this.currentSession.timer.timeRemaining = newSegmentDuration;
    this.currentSession.timer.startedAt = Utils.getCurrentTimestamp();
    this.currentSession.timer.startedSegment = this.currentSession.timer.currentSegment;
    this.currentSession.timer.pausedAt = 0;
    this.currentSession.timer.timePaused = 0;
  }

  /**
   * Calculate timer state from stored timestamps (for offline recovery)
   */
  sync() {
    if (!this.currentSession || !this.currentSession.timer.isRunning) {
      return;
    }

    const now = Utils.getCurrentTimestamp();
    const segments = this.currentSession.segments.items;

    if (!this.currentSession.timer.startedAt || segments.length === 0) {
      return;
    }

    // Calculate actual elapsed time
    const actualElapsed = now - this.currentSession.timer.startedAt - this.currentSession.timer.timePaused - (this.currentSession.timer.isPaused && this.currentSession.timer.pausedAt > 0 ? now - this.currentSession.timer.pausedAt : 0);

    // Find current segment starting from startedSegment
    let currentSegment = this.currentSession.timer.startedSegment;
    let remainingElapsed = actualElapsed;

    // Debug logging
    console.log('Timer sync:', {
      now,
      startedAt: this.currentSession.timer.startedAt,
      timePaused: this.currentSession.timer.timePaused,
      actualElapsed,
      currentSegment,
      remainingElapsed
    });

    while (remainingElapsed > 0) {
      const segmentDuration = segments[currentSegment].duration * 1000;
      if (remainingElapsed < segmentDuration) break;

      remainingElapsed -= segmentDuration;
      currentSegment++;

      // Handle repeat mode wraparound
      if (currentSegment >= segments.length && this.currentSession.timer.repeat) {
        currentSegment = 0;
      }

      // Check if timer should stop (no repeat and reached end)
      if (currentSegment >= segments.length && !this.currentSession.timer.repeat) {
        this.currentSession.timer.isRunning = false;
        this.currentSession.timer.isPaused = false;
        this.currentSession.timer.currentSegment = 0;
        this.currentSession.timer.timeRemaining = segments[0].duration;
        this.resetTiming();
        return;
      }
    }

    // Calculate remaining time in current segment
    const timeRemaining = Math.max(0, Math.floor((segments[currentSegment].duration * 1000 - remainingElapsed) / 1000));

    // Debug logging
    console.log('Timer sync result:', {
      segmentDuration: segments[currentSegment].duration,
      remainingElapsed,
      calculatedTimeRemaining: timeRemaining,
      previousTimeRemaining: this.currentSession.timer.timeRemaining
    });

    // Update session state
    this.currentSession.timer.currentSegment = currentSegment;
    this.currentSession.timer.timeRemaining = timeRemaining;
  }



  /**
   * Get current segment object
   * @returns {Object|null} Current segment or null
   */
  getCurrentSegment() {
    if (!this.currentSession || !this.currentSession.segments.items) {
      return null;
    }
    
    const index = this.currentSession.timer.currentSegment;
    return this.currentSession.segments.items[index] || null;
  }

  /**
   * Get current segment duration in seconds
   * @returns {number} Duration in seconds
   */
  getCurrentSegmentDuration() {
    const segment = this.getCurrentSegment();
    return segment ? segment.duration : 1500; // Default 25 minutes
  }

  /**
   * Update timer display
   */
  updateDisplay() {
    const timerDisplay = Utils.DOM.getId('timerDisplay');
    const segmentName = Utils.DOM.getId('segmentName');
    const segmentProgress = Utils.DOM.getId('segmentProgress');
    const startBtn = Utils.DOM.getId('startBtn');
    const pauseBtn = Utils.DOM.getId('pauseBtn');
    const nextBtn = Utils.DOM.getId('nextBtn');

    if (!this.currentSession) {
      if (timerDisplay) timerDisplay.textContent = '25:00';
      if (segmentName) segmentName.textContent = 'Focus';
      if (segmentProgress) segmentProgress.textContent = '1 of 2';
      return;
    }

    // Update timer display
    if (timerDisplay) {
      const timeRemaining = this.currentSession.timer.timeRemaining || this.getCurrentSegmentDuration();
      timerDisplay.textContent = Utils.formatTime(timeRemaining);
    }

    // Update segment info
    const currentSegment = this.getCurrentSegment();
    if (segmentName && currentSegment) {
      segmentName.textContent = currentSegment.name;
    }

    if (segmentProgress) {
      const current = this.currentSession.timer.currentSegment + 1;
      const total = this.currentSession.segments.items.length;
      segmentProgress.textContent = `${current} of ${total}`;
    }

    // Update button states
    const isRunning = this.currentSession.timer.isRunning;
    const isPaused = this.currentSession.timer.isPaused;

    if (startBtn) {
      startBtn.style.display = (!isRunning || isPaused) ? 'flex' : 'none';
      startBtn.textContent = isPaused ? 'Resume' : 'Start';
    }

    if (pauseBtn) {
      pauseBtn.style.display = (isRunning && !isPaused) ? 'flex' : 'none';
    }

    // Next button is always visible
    if (nextBtn) {
      nextBtn.style.display = 'flex';
    }

    // Apply custom CSS if segment has it
    if (currentSegment && currentSegment.customCSS) {
      this.applyCustomCSS(currentSegment.customCSS);
    }
  }

  /**
   * Update timer state classes on body
   */
  updateTimerState() {
    const body = document.body;
    
    if (!this.currentSession) {
      body.className = 'timer-stopped';
      return;
    }

    const isRunning = this.currentSession.timer.isRunning;
    const isPaused = this.currentSession.timer.isPaused;

    // Remove all timer state classes
    body.classList.remove('timer-running', 'timer-paused', 'timer-stopped');

    // Add appropriate class
    if (isRunning && !isPaused) {
      body.classList.add('timer-running');
    } else if (isRunning && isPaused) {
      body.classList.add('timer-paused');
    } else {
      body.classList.add('timer-stopped');
    }
  }

  /**
   * Apply custom CSS from segment
   * @param {string} css - Custom CSS string
   */
  applyCustomCSS(css) {
    // Remove existing custom style
    const existingStyle = Utils.DOM.getId('customSegmentCSS');
    if (existingStyle) {
      existingStyle.remove();
    }

    if (css.trim()) {
      const style = Utils.DOM.create('style', { 
        id: 'customSegmentCSS',
        type: 'text/css' 
      });
      style.textContent = css;
      document.head.appendChild(style);
    }
  }

  // WebSocket event handlers
  handleSessionJoined(event) {
    const { sessionId, session } = event.detail;
    this.currentSessionId = sessionId;
    this.currentSession = session;

    // Ensure client has all required timer properties
    if (!this.currentSession.timer.hasOwnProperty('startedSegment')) {
      this.currentSession.timer.startedSegment = 0;
    }
    if (!this.currentSession.timer.hasOwnProperty('timePaused')) {
      this.currentSession.timer.timePaused = 0;
    }

    // Use server timer state when joining existing session
    if (session.timer.isRunning) {
      // Set start time based on server's remaining time
      const now = Utils.getCurrentTimestamp();
      const currentSegmentDuration = session.segments.items[session.timer.currentSegment]?.duration || 1500;
      const elapsedTime = currentSegmentDuration - session.timer.timeRemaining;

      this.currentSession.timer.startedAt = now - (elapsedTime * 1000);
      this.currentSession.timer.startedSegment = session.timer.currentSegment;
      this.currentSession.timer.timePaused = 0;

      if (session.timer.isPaused) {
        this.currentSession.timer.pausedAt = now;
      } else {
        this.currentSession.timer.pausedAt = 0;
      }
    }

    // Update local storage
    Utils.Storage.saveSession(sessionId, this.currentSession);

    this.updateDisplay();
    this.updateTimerState();

    // Start local timer if running
    if (session.timer.isRunning && !session.timer.isPaused) {
      this.startLocalTimer();
    }
  }

  handleSessionCreated(event) {
    const { sessionId } = event.detail;
    this.currentSessionId = sessionId;
    
    // Create default session
    this.currentSession = Utils.createDefaultSession(sessionId);
    Utils.Storage.saveSession(sessionId, this.currentSession);
    
    // Send initial segments to server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.updateSegments(sessionId, this.currentSession.segments);
    }
    
    this.updateDisplay();
    this.updateTimerState();
  }

  handleTimerUpdated(event) {
    const {
      currentSegment,
      timeRemaining,
      isRunning,
      isPaused,
      repeat
    } = event.detail;

    if (!this.currentSession) return;

    // Check for segment mismatch - server segment differs from local client segment
    const segmentChanged = currentSegment !== this.currentSession.timer.currentSegment;

    // Handle pause state changes (before updating state)
    const wasPaused = this.currentSession.timer.isPaused;
    const wasRunning = this.currentSession.timer.isRunning;

    // Update timer state from server
    this.currentSession.timer.currentSegment = currentSegment;
    this.currentSession.timer.isRunning = isRunning;
    this.currentSession.timer.isPaused = isPaused;
    this.currentSession.timer.repeat = repeat;

    // Handle segment changes - receiving clients need to sync to server's segment
    if (segmentChanged) {
      // Server segment differs from local - use server's timeRemaining and reset timing
      this.currentSession.timer.timeRemaining = timeRemaining;
      this.resetTiming();

      // If timer is running, set up new timing baseline for the new segment
      if (isRunning) {
        const now = Utils.getCurrentTimestamp();
        const currentSegmentDuration = this.getCurrentSegmentDuration();
        const elapsedTime = currentSegmentDuration - timeRemaining;

        this.currentSession.timer.startedAt = now - (elapsedTime * 1000);
        this.currentSession.timer.startedSegment = currentSegment;
        this.currentSession.timer.timePaused = 0;
        this.currentSession.timer.pausedAt = isPaused ? now : 0;
      }
    } else {
      // No segment change - handle normal pause/unpause transitions
      if (wasRunning && wasPaused && isRunning && !isPaused) {
        // Transitioning from paused to running - accumulate pause time
        if (this.currentSession.timer.pausedAt > 0) {
          const now = Utils.getCurrentTimestamp();
          this.currentSession.timer.timePaused += now - this.currentSession.timer.pausedAt;
          this.currentSession.timer.pausedAt = 0;
        }
      } else if (wasRunning && !wasPaused && isRunning && isPaused) {
        // Transitioning from running to paused - set pause timestamp
        this.currentSession.timer.pausedAt = Utils.getCurrentTimestamp();
      }

      // Reset client timing values when timer is stopped
      if (!isRunning) {
        this.resetTiming();
        this.currentSession.timer.timeRemaining = timeRemaining; // Use server value when stopped
      } else if (isRunning && !this.currentSession.timer.startedAt) {
        // Timer is running but client doesn't have timing baseline - set it up
        // Use client's own timeRemaining, or server's if client doesn't have one
        const clientTimeRemaining = this.currentSession.timer.timeRemaining || timeRemaining;
        const now = Utils.getCurrentTimestamp();
        const currentSegmentDuration = this.getCurrentSegmentDuration();
        const elapsedTime = currentSegmentDuration - clientTimeRemaining;

        this.currentSession.timer.startedAt = now - (elapsedTime * 1000);
        this.currentSession.timer.startedSegment = currentSegment;
        this.currentSession.timer.timePaused = 0;
        this.currentSession.timer.pausedAt = isPaused ? now : 0;

        // Only set timeRemaining from server if client doesn't have one
        if (!this.currentSession.timer.timeRemaining) {
          this.currentSession.timer.timeRemaining = timeRemaining;
        }
      }
    }

    // Start or stop local timer based on server state
    if (isRunning && !isPaused) {
      this.startLocalTimer();
    } else {
      this.stopLocalTimer();
    }

    // Save updated session
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);

    this.updateDisplay();
    this.updateTimerState();
  }

  handleSegmentsUpdated(event) {
    const { segments } = event.detail;

    if (!this.currentSession) return;

    // Update segments data
    this.currentSession.segments = segments;

    // Handle timer state when segments change
    if (this.currentSession.timer.currentSegment >= segments.items.length) {
      // Current segment was deleted, reset to first segment
      this.currentSession.timer.currentSegment = 0;
      this.currentSession.timer.timeRemaining = this.getCurrentSegmentDuration();
      if (!this.currentSession.timer.isRunning) {
        this.currentSession.timer.startedAt = 0;
        this.currentSession.timer.startedSegment = 0;
        this.currentSession.timer.pausedAt = 0;
        this.currentSession.timer.timePaused = 0;
      }
    } else if (this.currentSession.timer.isRunning) {
      // Timer is running and segment durations may have changed
      // Recalculate baseline to maintain accuracy
      const now = Utils.getCurrentTimestamp();
      const currentPosition = now - this.currentSession.timer.startedAt - this.currentSession.timer.timePaused - (this.currentSession.timer.isPaused && this.currentSession.timer.pausedAt > 0 ? now - this.currentSession.timer.pausedAt : 0);

      // Update baseline
      this.currentSession.timer.startedAt = now - currentPosition;
      this.currentSession.timer.startedSegment = this.currentSession.timer.currentSegment;
      this.currentSession.timer.timePaused = 0;
      this.currentSession.timer.pausedAt = 0;
    } else {
      // Timer is stopped, just update remaining time if needed
      const currentSegmentDuration = this.getCurrentSegmentDuration();
      if (this.currentSession.timer.timeRemaining > currentSegmentDuration) {
        this.currentSession.timer.timeRemaining = currentSegmentDuration;
      }
    }

    // Save updated session
    Utils.Storage.saveSession(this.currentSessionId, this.currentSession);

    this.updateDisplay();
  }

  /**
   * Get current timer state
   * @returns {Object} Timer state
   */
  getTimerState() {
    if (!this.currentSession) {
      return {
        isRunning: false,
        isPaused: false,
        currentSegment: 0,
        timeRemaining: 1500,
        repeat: false
      };
    }

    return {
      isRunning: this.currentSession.timer.isRunning,
      isPaused: this.currentSession.timer.isPaused,
      currentSegment: this.currentSession.timer.currentSegment,
      timeRemaining: this.currentSession.timer.timeRemaining,
      repeat: this.currentSession.timer.repeat
    };
  }

  /**
   * Dispose timer resources
   */
  dispose() {
    this.stopLocalTimer();
    this.currentSession = null;
    this.currentSessionId = null;
  }
}

// Create global timer instance
const timer = new Timer();

// Export to global scope
window.Timer = Timer;
window.timer = timer;