<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timeshare - Collaborative Pomodoro Timer</title>
    <meta name="description" content="A collaborative Pomodoro-like timer with real-time synchronization">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="/css/styles.css">
</head>
<body class="timer-stopped">
    <!-- Audio unlock button -->
    <button id="audioUnlockBtn" class="audio-unlock-btn" title="Click to enable audio alerts">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
        </svg>
    </button>

    <!-- Main application container -->
    <div id="app" class="app">
        <!-- Header with controls -->
        <header class="header">
            <div class="header-left">
                <button id="sessionsBtn" class="btn btn-secondary" title="View all sessions">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                    </svg>
                    Sessions
                </button>
                <button id="shareBtn" class="btn btn-secondary" title="Share session">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z"/>
                    </svg>
                    Share
                </button>
            </div>
            <div class="header-center">
                <h1 id="sessionName" class="session-name">Pomodoro Session</h1>
                <p id="sessionDescription" class="session-description">Focus and break timer</p>
            </div>
            <div class="header-right">
                <div id="connectedUsers" class="connected-users">
                    <span class="user-count">0 users connected</span>
                </div>
                <button id="userBtn" class="user-btn" title="Edit profile">
                    <img id="userAvatar" class="user-avatar" src="" alt="User avatar">
                    <div class="user-status-indicator"></div>
                </button>
                <button id="settingsBtn" class="btn btn-secondary" title="Settings">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    Settings
                </button>
            </div>
        </header>

        <!-- Main timer display -->
        <main class="main">
            <div class="timer-container">
                <div class="segment-info">
                    <span id="segmentName" class="segment-name">Focus</span>
                    <span id="segmentProgress" class="segment-progress">1 of 2</span>
                </div>
                <div id="timerDisplay" class="timer-display">25:00</div>
                <div class="timer-controls">
                    <button id="startBtn" class="btn btn-primary timer-btn">Start</button>
                    <button id="pauseBtn" class="btn btn-secondary timer-btn" style="display: none;">Pause</button>
                    <button id="stopBtn" class="btn btn-secondary timer-btn">Stop</button>
                    <button id="nextBtn" class="btn btn-secondary timer-btn" title="Next segment">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        Next
                    </button>
                </div>
            </div>
        </main>

        <!-- Connection status -->
        <div id="connectionStatus" class="connection-status">
            <span class="status-indicator"></span>
            <span class="status-text">Connecting...</span>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <button class="modal-close" data-close="settingsModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Session</h3>
                    <div class="form-group">
                        <label for="sessionNameInput">Session Name</label>
                        <input type="text" id="sessionNameInput" class="form-control" placeholder="Pomodoro Session">
                    </div>
                    <div class="form-group">
                        <label for="sessionDescInput">Description</label>
                        <input type="text" id="sessionDescInput" class="form-control" placeholder="Focus and break timer">
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="repeatModeInput">
                            <span class="checkmark"></span>
                            Repeat timer after completing all segments
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h3>Segments</h3>
                        <div class="segment-actions">
                            <button id="addSegmentBtn" class="btn btn-small btn-primary">Add Segment</button>
                            <button id="importBtn" class="btn btn-small btn-secondary">Import</button>
                            <button id="exportBtn" class="btn btn-small btn-secondary">Export</button>
                        </div>
                    </div>
                    <div id="segmentsList" class="segments-list">
                        <!-- Segments will be dynamically populated -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>User Profile</h2>
                <button class="modal-close" data-close="userModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="user-profile">
                    <div class="user-avatar-large">
                        <img id="userAvatarLarge" src="" alt="User avatar">
                    </div>
                    <div class="form-group">
                        <label for="userNameInput">Name</label>
                        <input type="text" id="userNameInput" class="form-control" placeholder="Your name">
                    </div>
                    <div class="form-group">
                        <label for="userEmailInput">Email</label>
                        <input type="email" id="userEmailInput" class="form-control" placeholder="<EMAIL>">
                        <small class="form-help">Used for Gravatar profile image</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sessions Modal -->
    <div id="sessionsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Sessions</h2>
                <button class="modal-close" data-close="sessionsModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="sessions-actions">
                    <button id="clearAllSessionsBtn" class="btn btn-secondary">Clear All Sessions</button>
                </div>
                <div id="sessionsList" class="sessions-list">
                    <!-- Sessions will be dynamically populated -->
                </div>
            </div>
        </div>
    </div>

    <!-- Import file input (hidden) -->
    <input type="file" id="importFileInput" accept=".json" style="display: none;">

    <!-- JavaScript modules -->
    <script src="/js/utils.js"></script>
    <script src="/js/alerts.js"></script>
    <script src="/js/websocket.js"></script>
    <script src="/js/timer.js"></script>
    <script src="/js/sessions.js"></script>
    <script src="/js/user.js"></script>
    <script src="/js/share.js"></script>
    <script src="/js/settings.js"></script>
    <script src="/js/app.js"></script>
</body>
</html>