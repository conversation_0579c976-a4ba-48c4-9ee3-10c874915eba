/**
 * Main application controller
 */

/**
 * Timer application class
 */
class TimerApp {
  constructor() {
    this.isInitialized = false;
    this.components = {};
    this.wasDisconnected = false;

    this.initialize();
  }

  /**
   * Initialize the application
   */
  initialize() {
    if (this.isInitialized) return;

    console.log('Initializing Timer App...');

    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      Utils.Events.on(document, 'DOMContentLoaded', () => this.initializeApp());
    } else {
      this.initializeApp();
    }
  }

  /**
   * Initialize application components
   */
  initializeApp() {
    try {
      // Initialize components in the correct order
      this.initializeComponents();
      
      // Setup global event handlers
      this.setupGlobalEventHandlers();
      
      // Connect to WebSocket
      this.connectWebSocket();
      
      // Setup keyboard shortcuts
      this.setupKeyboardShortcuts();
      
      // Setup page visibility handling
      this.setupPageVisibility();
      
      // Initialize session from URL or create new one
      this.initializeSession();
      
      this.isInitialized = true;
      console.log('Timer App initialized successfully');
      
      // Dispatch app ready event
      Utils.Events.dispatch(document, 'appReady');
      
    } catch (error) {
      console.error('Failed to initialize app:', error);
      this.handleInitializationError(error);
    }
  }

  /**
   * Initialize application components
   */
  initializeComponents() {
    // Components should already be initialized via their respective scripts
    this.components = {
      utils: window.Utils,
      alerts: window.alerts,
      websocket: window.ws,
      timer: window.timer,
      sessions: window.sessions,
      user: window.user,
      share: window.share,
      settings: window.settings
    };

    // Verify all components are available
    const missingComponents = [];
    Object.entries(this.components).forEach(([name, component]) => {
      if (!component) {
        missingComponents.push(name);
      }
    });

    if (missingComponents.length > 0) {
      throw new Error(`Missing components: ${missingComponents.join(', ')}`);
    }
  }

  /**
   * Setup global event handlers
   */
  setupGlobalEventHandlers() {
    // Handle uncaught errors
    Utils.Events.on(window, 'error', this.handleGlobalError.bind(this));
    Utils.Events.on(window, 'unhandledrejection', this.handleUnhandledRejection.bind(this));

    // Handle beforeunload to save state
    Utils.Events.on(window, 'beforeunload', this.handleBeforeUnload.bind(this));

    // Handle focus/blur for pause/resume
    Utils.Events.on(window, 'focus', this.handleWindowFocus.bind(this));
    Utils.Events.on(window, 'blur', this.handleWindowBlur.bind(this));

    // Handle WebSocket events
    Utils.Events.on(document, 'websocketConnected', this.handleWebSocketConnected.bind(this));
    Utils.Events.on(document, 'websocketDisconnected', this.handleWebSocketDisconnected.bind(this));

    // Handle audio unlock
    Utils.Events.on(document, 'audioUnlocked', this.handleAudioUnlocked.bind(this));
  }

  /**
   * Connect to WebSocket server
   */
  connectWebSocket() {
    if (this.components.websocket) {
      this.components.websocket.connect();
    }
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    Utils.Events.on(document, 'keydown', (event) => {
      // Don't handle shortcuts when typing in inputs
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
      }

      // Handle shortcuts
      switch (event.key) {
        case ' ': // Spacebar - Start/Pause timer
          event.preventDefault();
          this.toggleTimer();
          break;
        
        case 'Escape': // Escape - Close modals
          this.closeAllModals();
          break;
        
        case 's': // S - Show settings
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.components.settings?.showSettings();
          }
          break;
        
        case 'u': // U - Show user profile
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.components.user?.showUserModal();
          }
          break;
        
        case 'l': // L - Show sessions list
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            this.components.sessions?.showSessionsList();
          }
          break;
      }
    });
  }

  /**
   * Setup page visibility handling
   */
  setupPageVisibility() {
    let visibilityChangeEvent = 'visibilitychange';
    
    // Handle vendor prefixes
    if (typeof document.hidden === 'undefined') {
      if (typeof document.webkitHidden !== 'undefined') {
        visibilityChangeEvent = 'webkitvisibilitychange';
      } else if (typeof document.mozHidden !== 'undefined') {
        visibilityChangeEvent = 'mozvisibilitychange';
      } else if (typeof document.msHidden !== 'undefined') {
        visibilityChangeEvent = 'msvisibilitychange';
      }
    }

    Utils.Events.on(document, visibilityChangeEvent, () => {
      if (document.hidden) {
        this.handlePageHidden();
      } else {
        this.handlePageVisible();
      }
    });
  }

  /**
   * Initialize session from URL or create new one
   */
  initializeSession() {
    // Sessions component should handle this automatically
    // This is just a fallback in case it doesn't
    if (!this.components.sessions?.getCurrentSessionId()) {
      console.warn('No session initialized, creating new one');
      this.components.sessions?.createNewSession();
    }
  }

  /**
   * Toggle timer start/pause
   */
  toggleTimer() {
    if (!this.components.timer) return;

    const timerState = this.components.timer.getTimerState();
    
    if (!timerState.isRunning || timerState.isPaused) {
      this.components.timer.start();
    } else {
      this.components.timer.pause();
    }
  }

  /**
   * Close all open modals
   */
  closeAllModals() {
    const modals = Utils.DOM.queryAll('.modal.show');
    modals.forEach(modal => {
      Utils.DOM.hideModal(modal.id);
    });
  }

  /**
   * Handle page focus
   */
  handleWindowFocus() {
    // Update timer display when page regains focus (browsers throttle intervals in background)
    if (this.components.timer) {
      this.components.timer.sync();
      this.components.timer.updateDisplay();
    }

    // Only rejoin session if WebSocket was disconnected and reconnected
    if (this.wasDisconnected && this.components.websocket?.isSocketConnected()) {
      const sessionId = this.components.sessions?.getCurrentSessionId();
      if (sessionId) {
        this.components.websocket.joinSession(sessionId);
        this.wasDisconnected = false;
      }
    }
  }

  /**
   * Handle page blur
   */
  handleWindowBlur() {
    // Save current state when page loses focus
    this.saveApplicationState();
  }

  /**
   * Handle page hidden
   */
  handlePageHidden() {
    this.saveApplicationState();
  }

  /**
   * Handle page visible
   */
  handlePageVisible() {
    // Reconnect WebSocket if needed
    if (this.components.websocket && !this.components.websocket.isSocketConnected()) {
      this.components.websocket.connect();
    }
  }

  /**
   * Save application state
   */
  saveApplicationState() {
    // Current state is automatically saved by individual components
    // This is just for any additional cleanup
    console.log('Saving application state...');
  }

  /**
   * Handle before unload
   * @param {BeforeUnloadEvent} event - Before unload event
   */
  handleBeforeUnload(event) {
    this.saveApplicationState();
    // No confirmation dialog - let user leave freely
  }

  /**
   * Handle global errors
   * @param {ErrorEvent} event - Error event
   */
  handleGlobalError(event) {
    console.error('Global error:', event.error);
    
    // Show user-friendly error message for critical errors
    if (event.error && event.error.message.includes('WebSocket')) {
      this.showErrorNotification('Connection error. Please refresh the page.');
    }
  }

  /**
   * Handle unhandled promise rejections
   * @param {PromiseRejectionEvent} event - Promise rejection event
   */
  handleUnhandledRejection(event) {
    console.error('Unhandled promise rejection:', event.reason);
    
    // Prevent default handling to avoid console noise
    event.preventDefault();
  }

  /**
   * Handle initialization error
   * @param {Error} error - Initialization error
   */
  handleInitializationError(error) {
    const errorMessage = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #f56565;
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        z-index: 10000;
        font-family: sans-serif;
      ">
        <h2>Application Failed to Load</h2>
        <p>There was an error initializing the timer application.</p>
        <p><strong>Error:</strong> ${error.message}</p>
        <button onclick="location.reload()" style="
          background: white;
          color: #f56565;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          margin-top: 10px;
        ">Reload Page</button>
      </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', errorMessage);
  }

  /**
   * Handle WebSocket connected
   */
  handleWebSocketConnected() {
    console.log('WebSocket connected');
    this.hideErrorNotification();

    // If this is a reconnection, mark that we need to rejoin session
    if (this.wasDisconnected) {
      const sessionId = this.components.sessions?.getCurrentSessionId();
      if (sessionId) {
        this.components.websocket.joinSession(sessionId);
        this.wasDisconnected = false;
      }
    }
  }

  /**
   * Handle WebSocket disconnected
   */
  handleWebSocketDisconnected() {
    console.log('WebSocket disconnected');
    this.wasDisconnected = true;
    this.showErrorNotification('Connection lost. Attempting to reconnect...');
  }

  /**
   * Handle audio unlocked
   */
  handleAudioUnlocked() {
    console.log('Audio context unlocked');
  }

  /**
   * Show error notification
   * @param {string} message - Error message
   */
  showErrorNotification(message) {
    // Remove existing error notification
    const existing = Utils.DOM.getId('errorNotification');
    if (existing) {
      existing.remove();
    }

    const notification = Utils.DOM.create('div', {
      id: 'errorNotification',
      style: `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: #f56565;
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 9999;
        font-size: 0.9rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      `
    }, message);

    document.body.appendChild(notification);
  }

  /**
   * Hide error notification
   */
  hideErrorNotification() {
    const notification = Utils.DOM.getId('errorNotification');
    if (notification) {
      notification.remove();
    }
  }

  /**
   * Get application status
   * @returns {Object} Application status
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      components: Object.keys(this.components),
      websocketConnected: this.components.websocket?.isSocketConnected() || false,
      audioReady: this.components.alerts?.isAudioReady() || false,
      currentSessionId: this.components.sessions?.getCurrentSessionId() || null,
      timerState: this.components.timer?.getTimerState() || null
    };
  }

  /**
   * Restart application
   */
  restart() {
    console.log('Restarting application...');
    
    // Disconnect WebSocket
    if (this.components.websocket) {
      this.components.websocket.disconnect();
    }
    
    // Dispose timer
    if (this.components.timer) {
      this.components.timer.dispose();
    }
    
    // Clear components
    this.components = {};
    this.isInitialized = false;
    
    // Reload page
    setTimeout(() => {
      location.reload();
    }, 100);
  }

  /**
   * Debug information
   * @returns {Object} Debug information
   */
  debug() {
    return {
      status: this.getStatus(),
      localStorage: {
        clientId: Utils.Storage.getClientId(),
        sessionData: Utils.Storage.getSessionData()
      },
      websocket: {
        connected: this.components.websocket?.isSocketConnected(),
        reconnectAttempts: this.components.websocket?.reconnectAttempts
      },
      timer: this.components.timer?.getTimerState(),
      user: this.components.user?.getCurrentUser()
    };
  }
}

// Initialize app when script loads
let app;

// Wait for all components to be loaded
if (window.Utils && window.alerts && window.ws && window.timer && 
    window.sessions && window.user && window.share && window.settings) {
  app = new TimerApp();
} else {
  // Wait for other scripts to load
  Utils.Events.on(window, 'load', () => {
    app = new TimerApp();
  });
}

// Export to global scope
window.TimerApp = TimerApp;
window.app = app;

// Debug helper
window.debugApp = () => {
  console.log('App Debug Info:', app?.debug());
  return app?.debug();
};