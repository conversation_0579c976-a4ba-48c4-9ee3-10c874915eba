/**
 * Settings panel functionality
 */

/**
 * Settings manager class
 */
class SettingsManager {
  constructor() {
    this.currentSessionId = null;
    this.isUpdating = false;
    this.expandedSegments = new Set(); // Track expanded segments
    
    this.setupEventListeners();
  }

  /**
   * Setup DOM event listeners
   */
  setupEventListeners() {
    // Settings button
    const settingsBtn = Utils.DOM.getId('settingsBtn');
    Utils.Events.on(settingsBtn, 'click', () => this.showSettings());

    // Session settings
    const sessionNameInput = Utils.DOM.getId('sessionNameInput');
    const sessionDescInput = Utils.DOM.getId('sessionDescInput');
    const repeatModeInput = Utils.DOM.getId('repeatModeInput');

    if (sessionNameInput) {
      Utils.Events.on(sessionNameInput, 'blur', this.saveSessionSettings.bind(this));
      Utils.Events.on(sessionNameInput, 'input', 
        Utils.debounce(this.saveSessionSettings.bind(this), 500));
    }

    if (sessionDescInput) {
      Utils.Events.on(sessionDescInput, 'blur', this.saveSessionSettings.bind(this));
      Utils.Events.on(sessionDescInput, 'input', 
        Utils.debounce(this.saveSessionSettings.bind(this), 500));
    }

    if (repeatModeInput) {
      Utils.Events.on(repeatModeInput, 'change', this.saveSessionSettings.bind(this));
    }

    // Segment management buttons
    const addSegmentBtn = Utils.DOM.getId('addSegmentBtn');
    const importBtn = Utils.DOM.getId('importBtn');
    const exportBtn = Utils.DOM.getId('exportBtn');
    const importFileInput = Utils.DOM.getId('importFileInput');

    Utils.Events.on(addSegmentBtn, 'click', () => this.addSegment());
    Utils.Events.on(importBtn, 'click', () => this.importConfig());
    Utils.Events.on(exportBtn, 'click', () => this.exportConfig());
    Utils.Events.on(importFileInput, 'change', this.handleFileImport.bind(this));

    // Session events
    Utils.Events.on(document, 'sessionJoined', this.handleSessionChanged.bind(this));
    Utils.Events.on(document, 'sessionCreated', this.handleSessionChanged.bind(this));
    Utils.Events.on(document, 'segmentsUpdated', this.handleSegmentsUpdated.bind(this));
  }

  /**
   * Show settings modal
   */
  showSettings() {
    this.currentSessionId = window.sessions?.getCurrentSessionId();
    if (!this.currentSessionId) {
      alert('No active session');
      return;
    }

    this.populateSettings();
    Utils.DOM.showModal('settingsModal');
  }

  /**
   * Populate settings with current session data
   */
  populateSettings() {
    const session = Utils.Storage.getSession(this.currentSessionId);
    if (!session) return;

    // Session settings
    const sessionNameInput = Utils.DOM.getId('sessionNameInput');
    const sessionDescInput = Utils.DOM.getId('sessionDescInput');
    const repeatModeInput = Utils.DOM.getId('repeatModeInput');

    if (sessionNameInput) sessionNameInput.value = session.name || '';
    if (sessionDescInput) sessionDescInput.value = session.description || '';
    if (repeatModeInput) repeatModeInput.checked = session.timer.repeat || false;

    // Render segments
    this.renderSegments();
  }

  /**
   * Save session settings
   */
  saveSessionSettings() {
    if (this.isUpdating || !this.currentSessionId) return;

    const session = Utils.Storage.getSession(this.currentSessionId);
    if (!session) return;

    const sessionNameInput = Utils.DOM.getId('sessionNameInput');
    const sessionDescInput = Utils.DOM.getId('sessionDescInput');
    const repeatModeInput = Utils.DOM.getId('repeatModeInput');

    let hasChanges = false;

    if (sessionNameInput && sessionNameInput.value !== session.name) {
      session.name = sessionNameInput.value.trim() || 'Pomodoro Session';
      hasChanges = true;
    }

    if (sessionDescInput && sessionDescInput.value !== session.description) {
      session.description = sessionDescInput.value.trim() || 'Focus and break timer';
      hasChanges = true;
    }

    if (repeatModeInput && repeatModeInput.checked !== session.timer.repeat) {
      session.timer.repeat = repeatModeInput.checked;
      hasChanges = true;
      
      // Update timer repeat mode via WebSocket
      if (window.ws && window.ws.isSocketConnected()) {
        window.ws.toggleRepeat(this.currentSessionId);
      }
    }

    if (hasChanges) {
      Utils.Storage.saveSession(this.currentSessionId, session);
      
      // Update header
      if (window.sessions) {
        window.sessions.updateSessionHeader(this.currentSessionId);
      }
      
      // Update timer display
      if (window.timer) {
        window.timer.updateDisplay();
      }
    }
  }

  /**
   * Render segments list
   */
  renderSegments() {
    const segmentsList = Utils.DOM.getId('segmentsList');
    if (!segmentsList) return;

    const session = Utils.Storage.getSession(this.currentSessionId);
    if (!session || !session.segments.items) return;

    segmentsList.innerHTML = '';

    session.segments.items.forEach((segment, index) => {
      const segmentElement = this.createSegmentElement(segment, index);
      segmentsList.appendChild(segmentElement);
    });
  }

  /**
   * Create segment element
   * @param {Object} segment - Segment data
   * @param {number} index - Segment index
   * @returns {HTMLElement} Segment element
   */
  createSegmentElement(segment, index) {
    const segmentItem = Utils.DOM.create('div', {
      className: 'segment-item',
      'data-index': index
    });

    // Check if this segment was previously expanded
    if (this.expandedSegments.has(index)) {
      segmentItem.classList.add('expanded');
    }

    // Segment header
    const segmentHeader = Utils.DOM.create('div', { className: 'segment-header' });
    
    const expandBtn = Utils.DOM.create('button', { 
      className: 'segment-expand'
    }, '▶');
    
    const nameInput = Utils.DOM.create('input', {
      type: 'text',
      className: 'segment-name-input',
      value: segment.name,
      placeholder: 'Segment name'
    });
    
    const durationContainer = Utils.DOM.create('div', { className: 'segment-duration' });
    const durationInput = Utils.DOM.create('input', {
      type: 'number',
      className: 'segment-duration-input',
      value: Math.floor(segment.duration / 60),
      min: 1,
      max: 1440,
      placeholder: '25'
    });
    const durationLabel = Utils.DOM.create('span', {}, 'min');
    
    durationContainer.appendChild(durationInput);
    durationContainer.appendChild(durationLabel);
    
    const deleteBtn = Utils.DOM.create('button', {
      className: 'segment-delete',
      title: 'Delete segment'
    }, '🗑');

    segmentHeader.appendChild(expandBtn);
    segmentHeader.appendChild(nameInput);
    segmentHeader.appendChild(durationContainer);
    segmentHeader.appendChild(deleteBtn);

    // Segment details (initially hidden)
    const segmentDetails = Utils.DOM.create('div', { className: 'segment-details' });
    
    const alertGroup = Utils.DOM.create('div', { className: 'form-group' });
    const alertLabel = Utils.DOM.create('label', {}, 'Alert Sound');
    const alertContainer = Utils.DOM.create('div', {
      style: 'display: flex; gap: 10px; align-items: center;'
    });
    const alertSelect = this.createAlertSelect(segment.alert);
    const testAlertBtn = Utils.DOM.create('button', {
      type: 'button',
      className: 'btn btn-small btn-secondary',
      title: 'Test alert sound'
    }, '🔊 Test');
    
    alertContainer.appendChild(alertSelect);
    alertContainer.appendChild(testAlertBtn);
    alertGroup.appendChild(alertLabel);
    alertGroup.appendChild(alertContainer);
    
    const cssGroup = Utils.DOM.create('div', { className: 'form-group' });
    const cssLabel = Utils.DOM.create('label', {}, 'Custom CSS');
    const cssTextarea = Utils.DOM.create('textarea', {
      className: 'segment-css-textarea',
      placeholder: 'Enter custom CSS for this segment...',
      value: segment.customCSS || ''
    });
    cssGroup.appendChild(cssLabel);
    cssGroup.appendChild(cssTextarea);
    
    segmentDetails.appendChild(alertGroup);
    segmentDetails.appendChild(cssGroup);

    segmentItem.appendChild(segmentHeader);
    segmentItem.appendChild(segmentDetails);

    // Event handlers
    Utils.Events.on(expandBtn, 'click', () => this.toggleSegmentExpanded(segmentItem));
    Utils.Events.on(segmentHeader, 'click', (e) => {
      if (e.target !== expandBtn && e.target !== deleteBtn && e.target !== nameInput && 
          e.target !== durationInput) {
        this.toggleSegmentExpanded(segmentItem);
      }
    });

    Utils.Events.on(nameInput, 'blur', () => this.saveSegmentData(index));
    Utils.Events.on(nameInput, 'input', Utils.debounce(() => this.saveSegmentData(index), 500));
    
    Utils.Events.on(durationInput, 'blur', () => this.saveSegmentData(index));
    Utils.Events.on(durationInput, 'input', Utils.debounce(() => this.saveSegmentData(index), 500));
    
    Utils.Events.on(alertSelect, 'change', () => this.saveSegmentData(index));
    
    Utils.Events.on(testAlertBtn, 'click', (e) => {
      e.stopPropagation();
      this.testAlert(alertSelect.value);
    });
    
    Utils.Events.on(cssTextarea, 'blur', () => this.saveSegmentData(index));
    Utils.Events.on(cssTextarea, 'input', Utils.debounce(() => this.saveSegmentData(index), 1000));
    
    Utils.Events.on(deleteBtn, 'click', (e) => {
      e.stopPropagation();
      this.deleteSegment(index);
    });

    return segmentItem;
  }

  /**
   * Create alert sound select element
   * @param {string} selectedAlert - Currently selected alert
   * @returns {HTMLElement} Select element
   */
  createAlertSelect(selectedAlert) {
    const select = Utils.DOM.create('select', { className: 'segment-alert-select' });
    
    const alerts = window.alerts ? window.alerts.getAlertSounds() : [
      { value: 'Default', name: 'Default Bell' }
    ];

    alerts.forEach(alert => {
      const option = Utils.DOM.create('option', {
        value: alert.value,
        selected: alert.value === selectedAlert
      }, alert.name);
      select.appendChild(option);
    });

    return select;
  }

  /**
   * Toggle segment expanded state
   * @param {HTMLElement} segmentItem - Segment item element
   */
  toggleSegmentExpanded(segmentItem) {
    const index = parseInt(segmentItem.getAttribute('data-index'));
    const isExpanded = segmentItem.classList.toggle('expanded');
    
    if (isExpanded) {
      this.expandedSegments.add(index);
    } else {
      this.expandedSegments.delete(index);
    }
  }

  /**
   * Test alert sound
   * @param {string} alertType - Alert type to test
   */
  testAlert(alertType) {
    if (window.alerts) {
      window.alerts.testAlert(alertType);
    }
  }

  /**
   * Save segment data
   * @param {number} index - Segment index
   */
  saveSegmentData(index) {
    if (this.isUpdating || !this.currentSessionId) return;

    const session = Utils.Storage.getSession(this.currentSessionId);
    if (!session || !session.segments.items[index]) return;

    const segmentItem = Utils.DOM.query(`[data-index="${index}"]`);
    if (!segmentItem) return;

    const nameInput = segmentItem.querySelector('.segment-name-input');
    const durationInput = segmentItem.querySelector('.segment-duration-input');
    const alertSelect = segmentItem.querySelector('.segment-alert-select');
    const cssTextarea = segmentItem.querySelector('.segment-css-textarea');

    const segment = session.segments.items[index];
    const oldDuration = segment.duration;
    
    if (nameInput) {
      segment.name = nameInput.value.trim() || 'Segment';
    }
    
    if (durationInput) {
      const minutes = parseInt(durationInput.value) || 1;
      segment.duration = Math.max(1, Math.min(1440, minutes)) * 60; // Convert to seconds
    }
    
    if (alertSelect) {
      segment.alert = alertSelect.value;
    }
    
    if (cssTextarea) {
      segment.customCSS = cssTextarea.value;
    }

    // Update timestamp
    session.segments.lastUpdated = Utils.getCurrentTimestamp();
    
    // Save to storage
    Utils.Storage.saveSession(this.currentSessionId, session);
    
    // Sync with server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.updateSegments(this.currentSessionId, session.segments);
    }
    
    // Update timer if this is the current segment and duration changed
    if (window.timer && window.timer.currentSession) {
      const timerState = window.timer.getTimerState();
      if (timerState.currentSegment === index) {
        if (!timerState.isRunning) {
          // Timer is stopped, update remaining time to new duration
          window.timer.currentSession.timer.timeRemaining = segment.duration;
          window.timer.updateDisplay();
        } else if (oldDuration !== segment.duration && segment.duration < timerState.timeRemaining) {
          // Timer is running and new duration is less than remaining time
          window.timer.currentSession.timer.timeRemaining = segment.duration;
          window.timer.updateDisplay();
        }
      }
    }
  }

  /**
   * Add new segment
   */
  addSegment() {
    if (!this.currentSessionId) return;

    const session = Utils.Storage.getSession(this.currentSessionId);
    if (!session) return;

    const newSegment = {
      name: 'New Segment',
      duration: 1500, // 25 minutes
      alert: 'Default',
      customCSS: ''
    };

    session.segments.items.push(newSegment);
    session.segments.lastUpdated = Utils.getCurrentTimestamp();
    
    Utils.Storage.saveSession(this.currentSessionId, session);
    
    // Re-render segments
    this.renderSegments();
    
    // Sync with server
    if (window.ws && window.ws.isSocketConnected()) {
      window.ws.updateSegments(this.currentSessionId, session.segments);
    }
  }

  /**
   * Delete segment
   * @param {number} index - Segment index to delete
   */
  deleteSegment(index) {
    if (!this.currentSessionId) return;

    const session = Utils.Storage.getSession(this.currentSessionId);
    if (!session || session.segments.items.length <= 1) {
      alert('Cannot delete the last segment');
      return;
    }

    if (confirm('Delete this segment?')) {
      session.segments.items.splice(index, 1);
      session.segments.lastUpdated = Utils.getCurrentTimestamp();
      
      // Reset timer if current segment was deleted
      if (window.timer && window.timer.currentSession) {
        const timerState = window.timer.getTimerState();
        if (timerState.currentSegment >= session.segments.items.length) {
          window.timer.currentSession.timer.currentSegment = 0;
          window.timer.currentSession.timer.timeRemaining = session.segments.items[0].duration;
          window.timer.currentSession.timer.isRunning = false;
          window.timer.currentSession.timer.isPaused = false;
        }
      }
      
      Utils.Storage.saveSession(this.currentSessionId, session);
      
      // Re-render segments
      this.renderSegments();
      
      // Sync with server
      if (window.ws && window.ws.isSocketConnected()) {
        window.ws.updateSegments(this.currentSessionId, session.segments);
      }
      
      // Update timer display
      if (window.timer) {
        window.timer.updateDisplay();
      }
    }
  }

  /**
   * Export configuration
   */
  exportConfig() {
    if (!this.currentSessionId) return;

    const config = window.sessions?.exportSessionConfig(this.currentSessionId);
    if (!config) {
      alert('No session data to export');
      return;
    }

    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = Utils.DOM.create('a', {
      href: URL.createObjectURL(dataBlob),
      download: `pomodoro-session-${this.currentSessionId}.json`
    });
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(link.href);
  }

  /**
   * Import configuration
   */
  importConfig() {
    const importFileInput = Utils.DOM.getId('importFileInput');
    if (importFileInput) {
      importFileInput.click();
    }
  }

  /**
   * Handle file import
   * @param {Event} event - File input change event
   */
  handleFileImport(event) {
    const file = event.target.files[0];
    if (!file) return;

    if (file.type !== 'application/json') {
      alert('Please select a valid JSON file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target.result);
        this.importSessionConfig(config);
      } catch (error) {
        alert('Invalid JSON file format');
        console.error('Import error:', error);
      }
    };
    
    reader.readAsText(file);
    
    // Reset file input
    event.target.value = '';
  }

  /**
   * Import session configuration
   * @param {Object} config - Configuration object
   */
  importSessionConfig(config) {
    if (!this.currentSessionId) return;

    try {
      window.sessions?.importSessionConfig(config, this.currentSessionId);
      this.populateSettings();
      alert('Configuration imported successfully');
    } catch (error) {
      alert('Failed to import configuration');
      console.error('Import error:', error);
    }
  }

  // Event handlers
  handleSessionChanged(event) {
    this.currentSessionId = event.detail.sessionId;
  }

  handleSegmentsUpdated(event) {
    if (!this.isUpdating && event.detail.sessionId === this.currentSessionId) {
      this.isUpdating = true;
      
      // Update local storage
      const session = Utils.Storage.getSession(this.currentSessionId);
      if (session) {
        session.segments = event.detail.segments;
        Utils.Storage.saveSession(this.currentSessionId, session);
      }
      
      // Re-render if settings modal is open
      const modal = Utils.DOM.getId('settingsModal');
      if (modal && modal.classList.contains('show')) {
        this.renderSegments();
      }
      
      this.isUpdating = false;
    }
  }

  /**
   * Get current session settings
   * @returns {Object} Current session settings
   */
  getCurrentSettings() {
    if (!this.currentSessionId) return null;
    
    const session = Utils.Storage.getSession(this.currentSessionId);
    return session ? {
      name: session.name,
      description: session.description,
      repeat: session.timer.repeat,
      segments: session.segments.items
    } : null;
  }
}

// Create global settings manager instance
const settingsManager = new SettingsManager();

// Export to global scope
window.SettingsManager = SettingsManager;
window.settings = settingsManager;